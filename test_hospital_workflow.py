#!/usr/bin/env python3
"""
Test script to verify the hospital workflow for viewing and confirming purchase requests
"""

import requests
import json
import time
import os

def test_hospital_workflow():
    """Test the complete hospital workflow"""
    
    base_url = "http://localhost:8000"
    
    # Test addresses (should match your .env file)
    buyer_address = "******************************************"  # Example buyer
    hospital_address = os.getenv('HOSPITAL_ADDRESS', "******************************************")
    
    print("=== Testing Hospital Workflow ===")
    print(f"Buyer Address: {buyer_address}")
    print(f"Hospital Address: {hospital_address}")
    
    # Step 1: Create a purchase request (as buyer)
    print("\n--- Step 1: Creating Purchase Request (as Buyer) ---")
    
    purchase_request = {
        "wallet_address": buyer_address,
        "template_hash": "QmTestHospitalWorkflow123456789",
        "template": {
            "data_types": ["diagnosis", "treatment"],
            "criteria": {"age": {"min": 25, "max": 65}},
            "max_records": 20,
            "purpose": "research"
        },
        "amount": 0.005
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/request", json=purchase_request)
        print(f"Purchase Request Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            request_id = result.get("request_id")
            print(f"✅ Purchase request created successfully")
            print(f"Request ID: {request_id}")
            print(f"Estimated Cost: {result.get('estimated_cost', 'N/A')} ETH")
        else:
            print(f"❌ Purchase request failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Purchase request error: {str(e)}")
        return None
    
    # Step 2: Hospital views pending requests
    print("\n--- Step 2: Hospital Views Pending Requests ---")
    
    try:
        response = requests.get(f"{base_url}/api/hospital/requests", params={"wallet_address": hospital_address})
        print(f"Hospital Requests Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            requests_list = result.get("requests", [])
            total_count = result.get("total_count", 0)
            
            print(f"✅ Hospital can view pending requests")
            print(f"Total pending requests: {total_count}")
            
            if requests_list:
                print("\nPending Requests:")
                for i, req in enumerate(requests_list[:3]):  # Show first 3
                    print(f"  {i+1}. Request ID: {req.get('request_id')}")
                    print(f"     Buyer: {req.get('buyer')}")
                    print(f"     Amount: {req.get('amount')} ETH")
                    print(f"     Status: {req.get('status')}")
                    print(f"     Template: {req.get('template', {}).get('data_types', [])}")
                    
                    # Check if our request is in the list
                    if req.get('request_id') == request_id:
                        print(f"     ✅ Our test request found in pending list!")
                        
            else:
                print("❌ No pending requests found")
                return None
                
        else:
            print(f"❌ Hospital requests failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Hospital requests error: {str(e)}")
        return None
    
    # Step 3: Hospital confirms/replies to the request
    print("\n--- Step 3: Hospital Confirms Request ---")
    
    hospital_reply = {
        "request_id": request_id,
        "wallet_address": hospital_address,
        "records_count": 15,
        "patients_count": 3,
        "price_per_record": 0.0005
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/reply", json=hospital_reply)
        print(f"Hospital Reply Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Hospital confirmation successful")
            print(f"Message: {result.get('message', 'Request confirmed')}")
            print(f"Records Count: {hospital_reply['records_count']}")
            print(f"Patients Count: {hospital_reply['patients_count']}")
            print(f"Price per Record: {hospital_reply['price_per_record']} ETH")
        else:
            print(f"❌ Hospital reply failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Hospital reply error: {str(e)}")
        return None
    
    # Step 4: Verify the request status changed
    print("\n--- Step 4: Verify Request Status Changed ---")
    
    try:
        response = requests.get(f"{base_url}/api/hospital/requests", params={"wallet_address": hospital_address})
        
        if response.status_code == 200:
            result = response.json()
            requests_list = result.get("requests", [])
            
            # Check if our request is still in pending (it shouldn't be)
            our_request_still_pending = any(req.get('request_id') == request_id for req in requests_list)
            
            if not our_request_still_pending:
                print(f"✅ Request {request_id} no longer in pending list (correctly moved to confirmed)")
            else:
                print(f"⚠️ Request {request_id} still in pending list (may need status update)")
                
        else:
            print(f"❌ Status verification failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Status verification error: {str(e)}")
    
    return request_id

def test_hospital_requests_endpoint():
    """Test the hospital requests endpoint specifically"""
    
    print("\n=== Testing Hospital Requests Endpoint ===")
    
    base_url = "http://localhost:8000"
    hospital_address = os.getenv('HOSPITAL_ADDRESS', "******************************************")
    
    # Test with correct hospital address
    print("\n--- Test 1: Correct Hospital Address ---")
    try:
        response = requests.get(f"{base_url}/api/hospital/requests", params={"wallet_address": hospital_address})
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Endpoint accessible")
            print(f"Total requests: {result.get('total_count', 0)}")
        else:
            print(f"❌ Endpoint error: {response.text}")
            
    except Exception as e:
        print(f"❌ Request error: {str(e)}")
    
    # Test with incorrect address
    print("\n--- Test 2: Incorrect Address (should still work but with warning) ---")
    try:
        fake_address = "0xFAKEADDRESS1234567890123456789012345678"
        response = requests.get(f"{base_url}/api/hospital/requests", params={"wallet_address": fake_address})
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Endpoint accessible (with warning)")
            print(f"Total requests: {result.get('total_count', 0)}")
        else:
            print(f"❌ Endpoint error: {response.text}")
            
    except Exception as e:
        print(f"❌ Request error: {str(e)}")

if __name__ == "__main__":
    print("Starting Hospital Workflow Tests...")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print("Press Ctrl+C to cancel, or Enter to continue...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\nTest cancelled.")
        exit(0)
    
    # Test the hospital requests endpoint
    test_hospital_requests_endpoint()
    
    # Test the complete workflow
    request_id = test_hospital_workflow()
    
    if request_id:
        print(f"\n=== Workflow Test Completed Successfully ===")
        print(f"Test Request ID: {request_id}")
        print("The hospital should now be able to see and confirm purchase requests!")
    else:
        print(f"\n=== Workflow Test Failed ===")
        print("Check the server logs for detailed error information.")
    
    print("\nNext steps for testing:")
    print("1. Check the hospital UI to see if pending requests appear")
    print("2. Try confirming a request through the hospital interface")
    print("3. Verify that confirmed requests move to the next workflow stage")
