#!/usr/bin/env python3
"""
Test script to verify the real signature implementation for sharing metadata
"""

import json
import hashlib
import time
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from cryptography.hazmat.primitives.asymmetric import rsa, padding as asym_padding
from cryptography.hazmat.primitives import hashes
from backend.crypto.key_manager import <PERSON><PERSON><PERSON><PERSON>

def test_signature_implementation():
    """Test the signature generation and verification for sharing metadata"""
    
    print("=== Testing Real Signature Implementation ===")
    
    # Initialize key manager
    key_manager = KeyManager()
    
    # Test addresses
    patient_address = "******************************************"
    doctor_address = "******************************************"
    
    print(f"Patient address: {patient_address}")
    print(f"Doctor address: {doctor_address}")
    
    # Get patient's keys (this will generate them if they don't exist)
    patient_private_key = key_manager.get_private_key(patient_address)
    patient_public_key = key_manager.get_public_key(patient_address)
    
    print(f"Generated patient keys successfully")
    
    # Create test sharing metadata (without signature)
    current_time = int(time.time())
    sharing_metadata_unsigned = {
        "patient_address": patient_address,
        "doctor_address": doctor_address,
        "record_cid": "QmTestRecordCID123456789",
        "original_cid": "QmOriginalRecordCID123456789",
        "encrypted_key": "deadbeef" * 32,  # Mock encrypted key
        "timestamp": current_time,
        "expiration": current_time + 30*24*60*60,  # 30 days
    }
    
    print(f"Created unsigned metadata: {json.dumps(sharing_metadata_unsigned, indent=2)}")
    
    # Test 1: Sign the metadata
    print("\n=== Test 1: Signing Metadata ===")
    
    try:
        # Create canonical representation
        canonical_data = json.dumps(sharing_metadata_unsigned, sort_keys=True, separators=(',', ':'))
        print(f"Canonical data: {canonical_data[:100]}...")
        
        # Create hash
        data_hash = hashlib.sha256(canonical_data.encode('utf-8')).digest()
        print(f"Data hash: {data_hash.hex()[:40]}...")
        
        # Sign the hash
        signature_bytes = patient_private_key.sign(
            data_hash,
            asym_padding.PSS(
                mgf=asym_padding.MGF1(hashes.SHA256()),
                salt_length=asym_padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        signature_hex = signature_bytes.hex()
        print(f"Generated signature: {signature_hex[:40]}... ({len(signature_bytes)} bytes)")
        
        # Add signature to metadata
        sharing_metadata = sharing_metadata_unsigned.copy()
        sharing_metadata["signature"] = signature_hex
        
        print("✅ Signature generation successful")
        
    except Exception as e:
        print(f"❌ Signature generation failed: {str(e)}")
        return False
    
    # Test 2: Verify the signature
    print("\n=== Test 2: Verifying Signature ===")
    
    try:
        # Extract signature and recreate unsigned metadata
        signature_hex_verify = sharing_metadata.get("signature")
        unsigned_metadata_verify = sharing_metadata.copy()
        del unsigned_metadata_verify["signature"]
        
        # Create canonical representation
        canonical_data_verify = json.dumps(unsigned_metadata_verify, sort_keys=True, separators=(',', ':'))
        
        # Create hash
        data_hash_verify = hashlib.sha256(canonical_data_verify.encode('utf-8')).digest()
        
        # Convert signature back to bytes
        signature_bytes_verify = bytes.fromhex(signature_hex_verify)
        
        # Verify signature
        patient_public_key.verify(
            signature_bytes_verify,
            data_hash_verify,
            asym_padding.PSS(
                mgf=asym_padding.MGF1(hashes.SHA256()),
                salt_length=asym_padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        print("✅ Signature verification successful")
        
    except Exception as e:
        print(f"❌ Signature verification failed: {str(e)}")
        return False
    
    # Test 3: Test with modified data (should fail)
    print("\n=== Test 3: Testing Tampered Data ===")
    
    try:
        # Modify the metadata
        tampered_metadata = sharing_metadata.copy()
        tampered_metadata["doctor_address"] = "0xTAMPERED123456789012345678901234567890"
        
        # Remove signature and recreate canonical data
        del tampered_metadata["signature"]
        canonical_data_tampered = json.dumps(tampered_metadata, sort_keys=True, separators=(',', ':'))
        data_hash_tampered = hashlib.sha256(canonical_data_tampered.encode('utf-8')).digest()
        
        # Try to verify with original signature (should fail)
        patient_public_key.verify(
            signature_bytes,
            data_hash_tampered,
            asym_padding.PSS(
                mgf=asym_padding.MGF1(hashes.SHA256()),
                salt_length=asym_padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        print("❌ Tampered data verification should have failed but didn't!")
        return False
        
    except Exception as e:
        print(f"✅ Tampered data correctly rejected: {str(e)}")
    
    # Test 4: Test the verification function from the API
    print("\n=== Test 4: Testing API Verification Function ===")
    
    try:
        # Import the verification function
        from backend.api import verify_sharing_metadata_signature
        
        # Test with valid signature
        result_valid = verify_sharing_metadata_signature(sharing_metadata, patient_public_key)
        if result_valid:
            print("✅ API verification function works with valid signature")
        else:
            print("❌ API verification function failed with valid signature")
            return False
        
        # Test with tampered data
        tampered_metadata_with_sig = sharing_metadata.copy()
        tampered_metadata_with_sig["doctor_address"] = "0xTAMPERED123456789012345678901234567890"
        
        result_invalid = verify_sharing_metadata_signature(tampered_metadata_with_sig, patient_public_key)
        if not result_invalid:
            print("✅ API verification function correctly rejects tampered data")
        else:
            print("❌ API verification function should have rejected tampered data")
            return False
            
    except Exception as e:
        print(f"❌ API verification function test failed: {str(e)}")
        return False
    
    print("\n=== All Tests Passed! ===")
    print("The real signature implementation is working correctly.")
    return True

if __name__ == "__main__":
    success = test_signature_implementation()
    sys.exit(0 if success else 1)
