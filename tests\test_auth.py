#!/usr/bin/env python3
"""
Authentication test script for the healthcare data sharing system.

This script tests the authentication system:
1. Challenge generation
2. Signature verification
3. Session management
4. Role-based access control

Usage:
    python tests/test_auth.py
"""

import os
import sys
import json
import time
import requests
import unittest
from web3 import Web3
from eth_account.messages import encode_defunct

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import project modules
from backend.auth_utils import generate_auth_challenge, verify_auth_signature, is_authenticated, get_role, assign_role

# Test accounts
TEST_ACCOUNTS = {
    "Patient 1": {
        "address": "******************************************",
        "private_key": "0x91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e",
        "role": "patient"
    },
    "Doctor 1": {
        "address": "******************************************",
        "private_key": "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80",
        "role": "doctor"
    },
    "Hospital 1": {
        "address": "******************************************",
        "private_key": "0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d",
        "role": "hospital"
    },
    "Buyer 1": {
        "address": "0x3Fa2c09c14453c7acaC39E3fd57e0c6F1da3f5ce",
        "private_key": "0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a",
        "role": "buyer"
    }
}

# API endpoint
API_URL = os.getenv("API_URL", "http://127.0.0.1:8000/api")

class TestAuth(unittest.TestCase):
    """Test class for authentication system."""

    def test_1_challenge_generation(self):
        """Test challenge generation."""
        print("\n=== Testing Challenge Generation ===")

        for account_name, account_info in TEST_ACCOUNTS.items():
            wallet_address = account_info["address"]

            # Generate challenge using the utility function
            challenge = generate_auth_challenge(wallet_address)
            print(f"Generated challenge for {account_name}: {challenge[:30]}...")
            self.assertIsNotNone(challenge)
            self.assertIn("Sign this message", challenge)

            # Generate challenge using the API
            try:
                response = requests.post(
                    f"{API_URL}/auth/challenge",
                    json={"wallet_address": wallet_address}
                )

                if response.status_code == 404 or response.status_code == 422:
                    # Try alternative URL
                    response = requests.post(
                        f"{API_URL}/api/auth/challenge",
                        json={"wallet_address": wallet_address}
                    )

                self.assertEqual(response.status_code, 200)
                data = response.json()
                self.assertEqual(data["status"], "success")
                self.assertIn("challenge", data)
                self.assertIn("wallet_address", data)
                self.assertEqual(data["wallet_address"], wallet_address)
                print(f"API challenge for {account_name}: {data['challenge'][:30]}...")
            except Exception as e:
                print(f"Error testing API challenge for {account_name}: {str(e)}")

        print("Challenge generation tests passed!")

    def test_2_signature_verification(self):
        """Test signature verification."""
        print("\n=== Testing Signature Verification ===")

        for account_name, account_info in TEST_ACCOUNTS.items():
            wallet_address = account_info["address"]
            private_key = account_info["private_key"]

            # Generate challenge
            challenge = generate_auth_challenge(wallet_address)

            # Sign the challenge
            message_hash = encode_defunct(text=challenge)
            w3 = Web3()
            signed_message = w3.eth.account.sign_message(message_hash, private_key=private_key)
            signature = signed_message.signature.hex()

            # Verify signature using the utility function
            result = verify_auth_signature(wallet_address, signature)
            print(f"Signature verification for {account_name}: {result}")
            self.assertTrue(result)

            # Verify signature using the API
            try:
                response = requests.post(
                    f"{API_URL}/auth/verify",
                    json={
                        "wallet_address": wallet_address,
                        "signature": signature
                    }
                )

                if response.status_code == 404 or response.status_code == 422:
                    # Try alternative URL
                    response = requests.post(
                        f"{API_URL}/api/auth/verify",
                        json={
                            "wallet_address": wallet_address,
                            "signature": signature
                        }
                    )

                self.assertEqual(response.status_code, 200)
                data = response.json()
                self.assertEqual(data["status"], "success")
                self.assertTrue(data["authenticated"])
                self.assertEqual(data["wallet_address"], wallet_address)
                self.assertIn("role", data)
                print(f"API verification for {account_name}: {data['authenticated']} as {data['role']}")
            except Exception as e:
                print(f"Error testing API verification for {account_name}: {str(e)}")

        print("Signature verification tests passed!")

    def test_3_session_management(self):
        """Test session management."""
        print("\n=== Testing Session Management ===")

        for account_name, account_info in TEST_ACCOUNTS.items():
            wallet_address = account_info["address"]

            # Check authentication status using the utility function
            authenticated = is_authenticated(wallet_address)
            print(f"Authentication status for {account_name}: {authenticated}")
            self.assertTrue(authenticated)

            # Check authentication status using the API
            try:
                response = requests.get(
                    f"{API_URL}/auth/status",
                    params={"wallet_address": wallet_address}
                )

                if response.status_code == 404 or response.status_code == 422:
                    # Try alternative URL
                    response = requests.get(
                        f"{API_URL}/api/auth/status",
                        params={"wallet_address": wallet_address}
                    )

                self.assertEqual(response.status_code, 200)
                data = response.json()
                self.assertEqual(data["status"], "success")
                self.assertTrue(data["authenticated"])
                self.assertEqual(data["wallet_address"], wallet_address)
                self.assertIn("role", data)
                print(f"API status for {account_name}: {data['authenticated']} as {data['role']}")
            except Exception as e:
                print(f"Error testing API status for {account_name}: {str(e)}")

            # Test logout using the API
            try:
                response = requests.post(
                    f"{API_URL}/auth/logout",
                    json={"wallet_address": wallet_address}
                )

                if response.status_code == 404 or response.status_code == 422:
                    # Try alternative URL
                    response = requests.post(
                        f"{API_URL}/api/auth/logout",
                        json={"wallet_address": wallet_address}
                    )

                self.assertEqual(response.status_code, 200)
                data = response.json()
                self.assertEqual(data["status"], "success")
                print(f"Logout for {account_name}: {data['status']}")

                # Verify logout
                authenticated = is_authenticated(wallet_address)
                self.assertFalse(authenticated)
                print(f"Authentication status after logout for {account_name}: {authenticated}")
            except Exception as e:
                print(f"Error testing API logout for {account_name}: {str(e)}")

        print("Session management tests passed!")

    def test_4_role_based_access(self):
        """Test role-based access control."""
        print("\n=== Testing Role-Based Access Control ===")

        for account_name, account_info in TEST_ACCOUNTS.items():
            wallet_address = account_info["address"]
            expected_role = account_info["role"]

            # Authenticate the account
            challenge = generate_auth_challenge(wallet_address)
            message_hash = encode_defunct(text=challenge)
            w3 = Web3()
            signed_message = w3.eth.account.sign_message(message_hash, private_key=account_info["private_key"])
            signature = signed_message.signature.hex()
            verify_auth_signature(wallet_address, signature)

            # Check role using the utility function
            role = get_role(wallet_address)
            print(f"Role for {account_name}: {role}")
            self.assertEqual(role, expected_role)

            # Test role assignment
            new_role = "test_role"
            success = assign_role(wallet_address, new_role)
            self.assertTrue(success)

            # Verify role assignment
            role = get_role(wallet_address)
            print(f"New role for {account_name}: {role}")
            self.assertEqual(role, new_role)

            # Restore original role
            success = assign_role(wallet_address, expected_role)
            self.assertTrue(success)

            # Verify role restoration
            role = get_role(wallet_address)
            print(f"Restored role for {account_name}: {role}")
            self.assertEqual(role, expected_role)

        print("Role-based access control tests passed!")

if __name__ == "__main__":
    unittest.main()
