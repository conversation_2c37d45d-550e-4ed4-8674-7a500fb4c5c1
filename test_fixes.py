#!/usr/bin/env python3
"""
Test script to verify the fixes for:
1. Backend API audit_trail KeyError
2. Streamlit DataFrame Gas Fee serialization
"""

import requests
import json
import pandas as pd
import os

# Test configuration
API_URL = "http://localhost:8000"
HOSPITAL_ADDRESS = "0x28B317594b44483D24EE8AdCb13A1b148497C6ba"

def test_audit_trail_fix():
    """Test the audit_trail KeyError fix in purchase reply endpoint"""
    print("Testing audit_trail KeyError fix...")
    
    # Find a purchase request that doesn't have audit_trail
    purchase_files = [f for f in os.listdir("local_storage/purchases") if f.endswith('.json')]
    
    for file in purchase_files:
        file_path = f"local_storage/purchases/{file}"
        try:
            with open(file_path, 'r') as f:
                purchase_data = json.load(f)
            
            # Check if this request lacks audit_trail
            if 'audit_trail' not in purchase_data:
                request_id = purchase_data.get('request_id', file.replace('.json', ''))
                print(f"Found purchase request without audit_trail: {request_id}")
                
                # Test the purchase reply endpoint
                payload = {
                    "request_id": request_id,
                    "wallet_address": HOSPITAL_ADDRESS,
                    "records_count": 5,
                    "patients_count": 2,
                    "price_per_record": 0.001
                }
                
                try:
                    response = requests.post(f"{API_URL}/api/purchase/reply", json=payload)
                    
                    if response.status_code == 200:
                        print("✅ SUCCESS: Purchase reply endpoint worked without audit_trail KeyError!")
                        result = response.json()
                        print(f"Response: {result.get('status', 'Unknown')}")
                        
                        # Verify audit_trail was added
                        with open(file_path, 'r') as f:
                            updated_data = json.load(f)
                        
                        if 'audit_trail' in updated_data:
                            print("✅ SUCCESS: audit_trail field was added to the purchase data!")
                            print(f"Audit trail entries: {len(updated_data['audit_trail'])}")
                        else:
                            print("❌ FAILURE: audit_trail field was not added")
                        
                        return True
                    else:
                        print(f"❌ FAILURE: API returned status {response.status_code}")
                        print(f"Error: {response.text}")
                        return False
                        
                except Exception as e:
                    print(f"❌ FAILURE: Exception occurred: {str(e)}")
                    return False
                    
        except Exception as e:
            print(f"Error reading {file}: {str(e)}")
            continue
    
    print("No purchase requests found without audit_trail field")
    return True

def test_dataframe_serialization_fix():
    """Test the DataFrame serialization fix for Gas Fee (ETH) column"""
    print("\nTesting DataFrame Gas Fee serialization fix...")
    
    # Create test data that would previously cause pyarrow.lib.ArrowTypeError
    test_data = [
        {
            "Type": "Purchase Request",
            "Status": "Completed", 
            "Timestamp": "2025-01-15 10:30:00",
            "TX Hash": "0x123...abc",
            "Gas Fee (ETH)": "N/A",  # This would cause the error
            "Request ID": "test-1"
        },
        {
            "Type": "Hospital Reply",
            "Status": "Completed",
            "Timestamp": "2025-01-15 11:00:00", 
            "TX Hash": "0x456...def",
            "Gas Fee (ETH)": 0.001234,  # This is fine
            "Request ID": "test-2"
        },
        {
            "Type": "Patient Fill",
            "Status": "Pending",
            "Timestamp": "2025-01-15 11:30:00",
            "TX Hash": "0x789...ghi", 
            "Gas Fee (ETH)": None,  # This should also be fine
            "Request ID": "test-3"
        }
    ]
    
    # Apply the same fix logic as in the Streamlit app
    cleaned_data = []
    for tx in test_data:
        cleaned_tx = tx.copy()
        
        # Convert gas_fee to proper data type for DataFrame serialization
        gas_fee = cleaned_tx.get("Gas Fee (ETH)")
        if gas_fee is None or gas_fee == "N/A":
            gas_fee = None
        else:
            try:
                gas_fee = float(gas_fee)
            except (ValueError, TypeError):
                gas_fee = None
        
        cleaned_tx["Gas Fee (ETH)"] = gas_fee
        cleaned_data.append(cleaned_tx)
    
    try:
        # Try to create a DataFrame - this would previously fail
        df = pd.DataFrame(cleaned_data)
        print("✅ SUCCESS: DataFrame created without serialization errors!")
        print(f"DataFrame shape: {df.shape}")
        print(f"Gas Fee column data types: {df['Gas Fee (ETH)'].dtype}")
        print(f"Gas Fee values: {df['Gas Fee (ETH)'].tolist()}")
        
        # Try to simulate what Streamlit does internally with pyarrow
        try:
            import pyarrow as pa
            arrow_table = pa.Table.from_pandas(df)
            print("✅ SUCCESS: DataFrame successfully converted to Arrow format!")
            return True
        except ImportError:
            print("⚠️  PyArrow not available, but DataFrame creation succeeded")
            return True
        except Exception as arrow_error:
            print(f"❌ FAILURE: Arrow conversion failed: {str(arrow_error)}")
            return False
            
    except Exception as e:
        print(f"❌ FAILURE: DataFrame creation failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Running fix verification tests...\n")
    
    # Test 1: Backend API audit_trail fix
    audit_trail_success = test_audit_trail_fix()
    
    # Test 2: DataFrame serialization fix  
    dataframe_success = test_dataframe_serialization_fix()
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"Audit Trail Fix: {'✅ PASSED' if audit_trail_success else '❌ FAILED'}")
    print(f"DataFrame Serialization Fix: {'✅ PASSED' if dataframe_success else '❌ FAILED'}")
    
    if audit_trail_success and dataframe_success:
        print("\n🎉 All fixes are working correctly!")
    else:
        print("\n⚠️  Some fixes need attention.")

if __name__ == "__main__":
    main()
