{"scheme": "cpy06", "type": "group", "key": "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"}