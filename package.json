{"name": "healthcare-data-sharing", "version": "1.0.0", "description": "Decentralized Healthcare Data Sharing on BASE Testnet", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "verify:sepolia": "hardhat verify --network sepolia"}, "keywords": ["healthcare", "blockchain", "ipfs", "base", "ethereum"], "author": "", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-verify": "^2.0.13", "@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "chai": "^4.3.7", "dotenv": "^16.0.3", "ethereum-waffle": "^4.0.10", "ethers": "^5.7.2", "hardhat": "^2.23.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.0"}}