# Codebase Analysis for: pygroupsig

## Directory Structure

```
└── pygroupsig
    ├── definitions.py (8683 bytes)
    ├── interfaces.py (2834 bytes)
    ├── schemes
    │   ├── bbs04.py (11915 bytes)
    │   ├── cpy06.py (22063 bytes)
    │   ├── dl21.py (15706 bytes)
    │   ├── dl21seq.py (7884 bytes)
    │   ├── gl19.py (20317 bytes)
    │   ├── klap20.py (13757 bytes)
    │   └── ps16.py (10716 bytes)
    ├── utils
    │   ├── constants.py (3537 bytes)
    │   ├── helpers.py (4385 bytes)
    │   ├── mcl.py (13550 bytes)
    │   └── spk.py (11280 bytes)
    └── __init__.py (3012 bytes)
```

## Summary

- Total files: 14
- Total directories: 2
- Analyzed size: 146.13 KB
- Total text file size (including ignored): 14.19 KB
- Total tokens: 40020
- Analyzed text content size: 141.48 KB

## File Contents

### pygroupsig\definitions.py

```
# mypy: disable-error-code="misc"

from typing import Literal, Type, TypeAlias

from typing_extensions import Any, overload

from pygroupsig.interfaces import Container, Scheme
from pygroupsig.schemes.bbs04 import (
    Group as GroupBBS04,
)
from pygroupsig.schemes.bbs04 import (
    GroupKey as GroupKeyBBS04,
)
from pygroupsig.schemes.bbs04 import (
    ManagerKey as ManagerKeyBBS04,
)
from pygroupsig.schemes.bbs04 import (
    MemberKey as MemberKeyBBS04,
)
from pygroupsig.schemes.bbs04 import (
    Signature as SignatureBBS04,
)
from pygroupsig.schemes.cpy06 import (
    Group as GroupCPY06,
)
from pygroupsig.schemes.cpy06 import (
    GroupKey as GroupKeyCPY06,
)
from pygroupsig.schemes.cpy06 import (
    ManagerKey as ManagerKeyCPY06,
)
from pygroupsig.schemes.cpy06 import (
    MemberKey as MemberKeyCPY06,
)
from pygroupsig.schemes.cpy06 import (
    Signature as SignatureCPY06,
)
from pygroupsig.schemes.dl21 import (
    Group as GroupDL21,
)
from pygroupsig.schemes.dl21 import (
    GroupKey as GroupKeyDL21,
)
from pygroupsig.schemes.dl21 import (
    ManagerKey as ManagerKeyDL21,
)
from pygroupsig.schemes.dl21 import (
    MemberKey as MemberKeyDL21,
)
from pygroupsig.schemes.dl21 import (
    Signature as SignatureDL21,
)
from pygroupsig.schemes.dl21seq import (
    Group as GroupDL21SEQ,
)
from pygroupsig.schemes.dl21seq import (
    GroupKey as GroupKeyDL21SEQ,
)
from pygroupsig.schemes.dl21seq import (
    ManagerKey as ManagerKeyDL21SEQ,
)
from pygroupsig.schemes.dl21seq import (
    MemberKey as MemberKeyDL21SEQ,
)
from pygroupsig.schemes.dl21seq import (
    Signature as SignatureDL21SEQ,
)
from pygroupsig.schemes.gl19 import (
    BlindKey as BlindKeyGL19,
)
from pygroupsig.schemes.gl19 import (
    Group as GroupGL19,
)
from pygroupsig.schemes.gl19 import (
    GroupKey as GroupKeyGL19,
)
from pygroupsig.schemes.gl19 import (
    ManagerKey as ManagerKeyGL19,
)
from pygroupsig.schemes.gl19 import (
    MemberKey as MemberKeyGL19,
)
from pygroupsig.schemes.gl19 import (
    Signature as SignatureGL19,
)
from pygroupsig.schemes.klap20 import (
    Group as GroupKLAP20,
)
from pygroupsig.schemes.klap20 import (
    GroupKey as GroupKeyKLAP20,
)
from pygroupsig.schemes.klap20 import (
    ManagerKey as ManagerKeyKLAP20,
)
from pygroupsig.schemes.klap20 import (
    MemberKey as MemberKeyKLAP20,
)
from pygroupsig.schemes.klap20 import (
    Signature as SignatureKLAP20,
)
from pygroupsig.schemes.ps16 import (
    Group as GroupPS16,
)
from pygroupsig.schemes.ps16 import (
    GroupKey as GroupKeyPS16,
)
from pygroupsig.schemes.ps16 import (
    ManagerKey as ManagerKeyPS16,
)
from pygroupsig.schemes.ps16 import (
    MemberKey as MemberKeyPS16,
)
from pygroupsig.schemes.ps16 import (
    Signature as SignaturePS16,
)

SCHEMES: dict[str, Any] = {
    "bbs04": (
        GroupBBS04,
        GroupKeyBBS04,
        ManagerKeyBBS04,
        MemberKeyBBS04,
        SignatureBBS04,
    ),
    "ps16": (
        GroupPS16,
        GroupKeyPS16,
        ManagerKeyPS16,
        MemberKeyPS16,
        SignaturePS16,
    ),
    "cpy06": (
        GroupCPY06,
        GroupKeyCPY06,
        ManagerKeyCPY06,
        MemberKeyCPY06,
        SignatureCPY06,
    ),
    "klap20": (
        GroupKLAP20,
        GroupKeyKLAP20,
        ManagerKeyKLAP20,
        MemberKeyKLAP20,
        SignatureKLAP20,
    ),
    "gl19": (
        GroupGL19,
        GroupKeyGL19,
        ManagerKeyGL19,
        MemberKeyGL19,
        BlindKeyGL19,
        SignatureGL19,
    ),
    "dl21": (
        GroupDL21,
        GroupKeyDL21,
        ManagerKeyDL21,
        MemberKeyDL21,
        SignatureDL21,
    ),
    "dl21seq": (
        GroupDL21SEQ,
        GroupKeyDL21SEQ,
        ManagerKeyDL21SEQ,
        MemberKeyDL21SEQ,
        SignatureDL21SEQ,
    ),
}

# This is so much boilerplate just to have to have static types...
str_BBS04: TypeAlias = Literal["bbs04"]
str_PS16: TypeAlias = Literal["ps16"]
str_CPY06: TypeAlias = Literal["cpy06"]
str_KLAP20: TypeAlias = Literal["klap20"]
str_GL19: TypeAlias = Literal["gl19"]
str_DL21: TypeAlias = Literal["dl21"]
str_DL21SEQ: TypeAlias = Literal["dl21seq"]
str_Group: TypeAlias = Literal["group"]
str_Manager: TypeAlias = Literal["manager"]
str_Member: TypeAlias = Literal["member"]
str_Blind: TypeAlias = Literal["blind"]


@overload
def group(scheme_name: str_BBS04) -> Type[GroupBBS04]: ...
@overload
def group(scheme_name: str_PS16) -> Type[GroupPS16]: ...
@overload
def group(scheme_name: str_CPY06) -> Type[GroupCPY06]: ...
@overload
def group(scheme_name: str_KLAP20) -> Type[GroupKLAP20]: ...
@overload
def group(scheme_name: str_GL19) -> Type[GroupGL19]: ...
@overload
def group(scheme_name: str_DL21) -> Type[GroupDL21]: ...
@overload
def group(scheme_name: str_DL21SEQ) -> Type[GroupDL21SEQ]: ...


def group(scheme_name: str) -> Type[Scheme]:
    try:
        return SCHEMES[scheme_name][0]
    except KeyError:
        raise ValueError(f"Unknown scheme: {scheme_name}")


# BBS04
@overload
def key(scheme_name: str_BBS04, key_type: str_Group) -> Type[GroupKeyBBS04]: ...
@overload
def key(
    scheme_name: str_BBS04, key_type: str_Manager
) -> Type[ManagerKeyBBS04]: ...
@overload
def key(
    scheme_name: str_BBS04, key_type: str_Member
) -> Type[MemberKeyBBS04]: ...


# PS16
@overload
def key(scheme_name: str_PS16, key_type: str_Group) -> Type[GroupKeyPS16]: ...
@overload
def key(
    scheme_name: str_PS16, key_type: str_Manager
) -> Type[ManagerKeyPS16]: ...
@overload
def key(scheme_name: str_PS16, key_type: str_Member) -> Type[MemberKeyPS16]: ...


# CPY06
@overload
def key(scheme_name: str_CPY06, key_type: str_Group) -> Type[GroupKeyCPY06]: ...
@overload
def key(
    scheme_name: str_CPY06, key_type: str_Manager
) -> Type[ManagerKeyCPY06]: ...
@overload
def key(
    scheme_name: str_CPY06, key_type: str_Member
) -> Type[MemberKeyCPY06]: ...


# KLAP20
@overload
def key(
    scheme_name: str_KLAP20, key_type: str_Group
) -> Type[GroupKeyKLAP20]: ...
@overload
def key(
    scheme_name: str_KLAP20, key_type: str_Manager
) -> Type[ManagerKeyKLAP20]: ...
@overload
def key(
    scheme_name: str_KLAP20, key_type: str_Member
) -> Type[MemberKeyKLAP20]: ...


# GL19
@overload
def key(scheme_name: str_GL19, key_type: str_Group) -> Type[GroupKeyGL19]: ...
@overload
def key(
    scheme_name: str_GL19, key_type: str_Manager
) -> Type[ManagerKeyGL19]: ...
@overload
def key(scheme_name: str_GL19, key_type: str_Member) -> Type[MemberKeyGL19]: ...
@overload
def key(scheme_name: str_GL19, key_type: str_Blind) -> Type[BlindKeyGL19]: ...


# DL21
@overload
def key(scheme_name: str_DL21, key_type: str_Group) -> Type[GroupKeyDL21]: ...
@overload
def key(
    scheme_name: str_DL21, key_type: str_Manager
) -> Type[ManagerKeyDL21]: ...
@overload
def key(scheme_name: str_DL21, key_type: str_Member) -> Type[MemberKeyDL21]: ...


# DL21SEQ
@overload
def key(
    scheme_name: str_DL21SEQ, key_type: str_Group
) -> Type[GroupKeyDL21SEQ]: ...
@overload
def key(
    scheme_name: str_DL21SEQ, key_type: str_Manager
) -> Type[ManagerKeyDL21SEQ]: ...
@overload
def key(
    scheme_name: str_DL21SEQ, key_type: str_Member
) -> Type[MemberKeyDL21SEQ]: ...


def key(scheme_name: str, key_type: str) -> Type[Container]:
    try:
        sch_data = SCHEMES[scheme_name]
    except KeyError:
        raise ValueError(f"Unknown scheme: {scheme_name}")
    keys = sch_data[1 : len(sch_data) - 1]
    key_types = [k.__name__.split("Key")[0].lower() for k in keys]
    try:
        return keys[key_types.index(key_type)]
    except ValueError:
        raise ValueError(f"Unknown key type: {key_type}")


@overload
def signature(scheme_name: str_BBS04) -> Type[SignatureBBS04]: ...
@overload
def signature(scheme_name: str_PS16) -> Type[SignaturePS16]: ...
@overload
def signature(scheme_name: str_CPY06) -> Type[SignatureCPY06]: ...
@overload
def signature(scheme_name: str_KLAP20) -> Type[SignatureKLAP20]: ...
@overload
def signature(scheme_name: str_GL19) -> Type[SignatureGL19]: ...
@overload
def signature(scheme_name: str_DL21) -> Type[SignatureDL21]: ...
@overload
def signature(scheme_name: str_DL21SEQ) -> Type[SignatureDL21SEQ]: ...


def signature(scheme_name: str) -> Type[Container]:
    try:
        sch_data = SCHEMES[scheme_name]
    except KeyError:
        raise ValueError(f"Unknown scheme: {scheme_name}")
    return sch_data[-1]

```

### pygroupsig\interfaces.py

```
from abc import ABC, abstractmethod
from logging import Logger
from typing import Any, Generic, KeysView, Type, TypeVar


class Container(ABC):
    _scheme_name: str
    _container_type: str

    @abstractmethod
    def info(self) -> tuple[str, str, KeysView]:
        """
        Listing of internal properties
        """

    @abstractmethod
    def to_b64(self) -> str:
        """
        Export internal properties to base64
        """

    @abstractmethod
    def set_b64(self, s: str | bytes) -> None:
        """
        Import base64 to internal properties
        """

    @classmethod
    @abstractmethod
    def from_b64(cls: Type["Container"], s: str | bytes) -> "Container":
        """
        Create new object from base64
        """


GroupKeyT = TypeVar("GroupKeyT", bound="Container")
ManagerKeyT = TypeVar("ManagerKeyT", bound="Container")
MemberKeyT = TypeVar("MemberKeyT", bound="Container")


class Scheme(Generic[GroupKeyT, ManagerKeyT, MemberKeyT], ABC):
    _scheme_name: str
    group_key: GroupKeyT
    manager_key: ManagerKeyT
    _logger: Logger

    @abstractmethod
    def setup(self) -> None:
        """
        Create a specific scheme instance, setting the group
        and manager keys, and the GML (if scheme implements it).
        """

    @staticmethod
    @abstractmethod
    def join_start() -> int:
        """
        Functions returning who sends the first message in the join protocol, i.e. 0=manager, 1=member.
        """

    @staticmethod
    @abstractmethod
    def join_seq() -> int:
        """
        Functions returning the number of messages to be exchanged in the join protocol.
        """

    @abstractmethod
    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        """
        Functions of this type are executed by group managers. From a
        partial member key, as produced by the corresponding join_mem
        function, these functions create a complete member key, adding the
        new member to any necessary component (e.g. GMLs).
        """

    @abstractmethod
    def join_mem(
        self, message: dict[str, Any], member_key: MemberKeyT
    ) -> dict[str, Any]:
        """
        Functions of this type are executed by entities who want to be
        included in a group. They run in coordination with the equivalent
        functions run by managers (join_mgr).
        """

    @abstractmethod
    def sign(self, message: str, member_key: MemberKeyT) -> dict[str, Any]:
        """
        Type of functions for signing messages.
        """

    @abstractmethod
    def verify(self, message: str, signature: str) -> dict[str, Any]:
        """
        Type of functions for verifying group signatures.
        """

```

### pygroupsig\schemes\bbs04.py

```
import hashlib
import logging
from typing import Any

from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    GML,
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "bbs04"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    g1: G1
    g2: G2
    h: G1
    u: G1
    v: G1
    w: G2
    hw: GT
    hg2: GT
    g1g2: GT

    def __init__(self) -> None:
        self.g1 = G1()  # Tr(g2)
        self.g2 = G2()  # andom generator of G2
        self.h = G1()  # Random element in G1 \ 1
        self.u = G1()  # h^(xi1^-1) @see bbs04_mgr_key_t
        self.v = G1()  # h^(xi2^-1) @see bbs04_mgr_key_t
        self.w = G2()  # g2^gamma @see bbs04_mgr_key_t
        # Optimizations
        self.hw = GT()  # Precompute e(h,w)
        self.hg2 = GT()  # Precompute e(h,g2)
        self.g1g2 = GT()  # Precompute e(g1,g2)


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    xi1: Fr
    xi2: Fr
    gamma: Fr

    def __init__(self) -> None:
        self.xi1 = Fr()  # Exponent for tracing signatures
        self.xi2 = Fr()  # Exponent for tracing signatures
        self.gamma = Fr()  # Exponent for generating member keys


class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    x: Fr
    A: G1
    Ag2: GT

    def __init__(self) -> None:
        self.x = Fr()  # 1st element of the member's key
        self.A = G1()  # 2nd element of the member's key, A = g_1^(1/(gamma+x))
        self.Ag2 = GT()  # Optimizations, e(sigma1,grpkey->Y)


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    T1: G1
    T2: G1
    T3: G1
    c: Fr
    salpha: Fr
    sbeta: Fr
    sx: Fr
    sdelta1: Fr
    sdelta2: Fr

    def __init__(self) -> None:
        self.T1 = G1()
        self.T2 = G1()
        self.T3 = G1()
        self.c = Fr()
        self.salpha = Fr()
        self.sbeta = Fr()
        self.sx = Fr()
        self.sdelta1 = Fr()
        self.sdelta2 = Fr()


class Group(
    JoinMixin, ReprMixin, MetadataMixin, Scheme[GroupKey, ManagerKey, MemberKey]
):
    _logger = logging.getLogger(__name__)

    gml: GML

    def __init__(self) -> None:
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()
        self.gml = GML()

    def setup(self) -> None:
        ## Select random generator g2 in G2. Since G2 is a cyclic multiplicative group
        ## of prime order, any element is a generator, so choose some random element.
        self.group_key.g2.set_random()
        # @TODO g1 is supposed to be the trace of g2...
        self.group_key.g1.set_random()

        # h random in G1 \ 1
        self.group_key.h.set_random()
        ## why?
        while self.group_key.h.is_zero():
            self.group_key.h.set_random()

        # xi1 and xi2 random in Z^*_p
        self.manager_key.xi1.set_random()
        self.manager_key.xi2.set_random()

        # u = h*(xi1**-1)
        self.group_key.u.set_object(self.group_key.h * ~self.manager_key.xi1)

        # v = h*(xi2**-1)
        self.group_key.v.set_object(self.group_key.h * ~self.manager_key.xi2)

        # gamma random in Z^*_p
        self.manager_key.gamma.set_random()

        # w = g_2*gamma
        self.group_key.w.set_object(self.group_key.g2 * self.manager_key.gamma)

        # hw = e(h,w)
        self.group_key.hw.set_object(
            GT.pairing(self.group_key.h, self.group_key.w)
        )

        # hg2 = e(h, g2)
        self.group_key.hg2.set_object(
            GT.pairing(self.group_key.h, self.group_key.g2)
        )

        # g1g2 = e(g2, g2)
        self.group_key.g1g2.set_object(
            GT.pairing(self.group_key.g1, self.group_key.g2)
        )

    @staticmethod
    def join_seq() -> int:
        return 1

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## x \in_R Z_p^*
            x = Fr.from_random()

            ## Compute A = g_1 * ((mgrkey->gamma+x)**-1)
            A = self.group_key.g1 * ~(self.manager_key.gamma + x)

            ## Optimization
            Ag2 = GT.pairing(A, self.group_key.g2)

            ## Update the GML
            h = hashlib.sha256(A.to_bytes())
            self.gml[h.hexdigest()] = (A,)

            ## Dump the key into a msg
            ret["status"] = "success"
            ret["x"] = x.to_b64()
            ret["A"] = A.to_b64()
            ret["Ag2"] = Ag2.to_b64()
            ret["phase"] = 1  # type: ignore
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, Any]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## Import the primitives sent by the manager
            member_key.x.set_b64(message["x"])
            member_key.A.set_b64(message["A"])
            member_key.Ag2.set_b64(message["Ag2"])

            ## Build the output message
            ret["status"] = "success"
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def sign(self, message: str, member_key: MemberKey) -> dict[str, Any]:
        message = str(message)

        # alpha,beta \in_R Zp
        alpha = Fr.from_random()
        beta = Fr.from_random()

        ## Compute T1,T2,T3
        # T1 = u*alpha
        sig = Signature()
        sig.T1.set_object(self.group_key.u * alpha)
        # T2 = v*beta
        sig.T2.set_object(self.group_key.v * beta)
        # T3 = A + h*(alpha+beta)
        alphabeta = alpha + beta
        sig.T3.set_object(member_key.A + (self.group_key.h * alphabeta))

        # delta1 = x*alpha
        delta1 = member_key.x * alpha
        # delta2 = x*beta
        delta2 = member_key.x * beta

        # ralpha, rbeta, rx, rdelta1, rdelta2 \in_R Zp
        ralpha = Fr.from_random()
        rbeta = Fr.from_random()
        rx = Fr.from_random()
        rdelta1 = Fr.from_random()
        rdelta2 = Fr.from_random()

        ## Compute R1, R2, R3, R4, R5
        # Optimized o1 = e(T3, g2) = e(h, g2)**(alpha+beta) * e(A, g2)
        aux_o1 = (self.group_key.hg2**alphabeta) * member_key.Ag2

        # R1 = u*ralpha
        R1 = self.group_key.u * ralpha
        # R2 = v*rbeta
        R2 = self.group_key.v * rbeta

        # R3 = e(T3,g2)^rx * e(h,w)^(-ralpha-rbeta) * e(h,g2)^(-rdelta1-rdelta2)
        # e1 = e(T3,g2)^rx
        aux_e1 = aux_o1**rx

        # e2 = e(h,w)**(-ralpha-rbeta)
        aux_e2 = self.group_key.hw ** ((-ralpha) - rbeta)

        # e3 = e(h,g2)**(-rdelta1-rdelta2)
        aux_e3 = self.group_key.hg2 ** ((-rdelta1) - rdelta2)

        # R3 = e1 * e2 * e3
        R3 = aux_e1 * aux_e2 * aux_e3

        # R4 = T1*rx + u*-rdelta1
        R4 = (sig.T1 * rx) + (self.group_key.u * (-rdelta1))

        # R5 = T2*rx + v*-rdelta2
        R5 = (sig.T2 * rx) + (self.group_key.v * (-rdelta2))

        # c = hash(M,T1,T2,T3,R1,R2,R3,R4,R5) \in Zp
        h = hashlib.sha256()
        h.update(message.encode())
        h.update(sig.T1.to_bytes())
        h.update(sig.T2.to_bytes())
        h.update(sig.T3.to_bytes())
        h.update(R1.to_bytes())
        h.update(R2.to_bytes())
        h.update(R3.to_bytes())
        h.update(R4.to_bytes())
        h.update(R5.to_bytes())

        ## Get c as the element associated to the obtained hash value
        sig.c.set_hash(h.digest())
        # salpha = ralpha + c*alpha
        sig.salpha.set_object(ralpha + (sig.c * alpha))
        # sbeta = rbeta + c*beta
        sig.sbeta.set_object(rbeta + (sig.c * beta))
        # sx = rx + c*x
        sig.sx.set_object(rx + (sig.c * member_key.x))
        # sdelta1 = rdelta1 + c*delta1
        sig.sdelta1.set_object(rdelta1 + (sig.c * delta1))
        # sdelta2 = rdelta2 + c*delta2
        sig.sdelta2.set_object(rdelta2 + (sig.c * delta2))

        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(self, message: str, signature: str) -> dict[str, Any]:
        message = str(message)
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)

        # R1 = u*salpha + T1*(-c)
        aux_neg = -sig.c
        R1 = (self.group_key.u * sig.salpha) + (sig.T1 * aux_neg)

        # R2 = v*sbeta + T2*(-c)
        R2 = (self.group_key.v * sig.sbeta) + (sig.T2 * aux_neg)

        ## R3 = e(T3,g2)^sx * e(h,w)^(-salpha-sbeta) * e(h,g2)^(-sdelta1-sdelta2) * (e(T3,w)/e(g1,g2))^c
        ## Optimized R3 =  e(h,w)^(-salpha-sbeta) * e(h,g2)^(-sdelta1-sdelta2) * e(T3, w^c * g2 ^ sx) * e(g1, g2)^-c

        ## Optimized e1 = e(T3, g2*sx + w*c)
        aux_e5 = (self.group_key.g2 * sig.sx) + (self.group_key.w * sig.c)
        aux_e1 = GT.pairing(sig.T3, aux_e5)

        # e2 = e(h,w)**(-salpha-sbeta)
        aux_neg = (-sig.salpha) - sig.sbeta
        aux_e2 = self.group_key.hw**aux_neg

        # e3 = e(h,g2)**(-sdelta1-sdelta2)
        aux_neg = (-sig.sdelta1) - sig.sdelta2
        aux_e3 = self.group_key.hg2**aux_neg

        # e4 = e(g1,g2)**-c
        aux_e4 = ~(self.group_key.g1g2**sig.c)

        # R3 = e1 * e2 * e3 * e4
        R3 = aux_e1 * aux_e2 * aux_e3 * aux_e4

        # R4 = T1*sx + u*(-sdelta1)
        R4 = (sig.T1 * sig.sx) + (self.group_key.u * (-sig.sdelta1))

        # R5 = T2*sx + v*(-sdelta2)
        R5 = (sig.T2 * sig.sx) + (self.group_key.v * (-sig.sdelta2))

        ## Recompute the hash-challenge c
        # c = hash(M,T1,T2,T3,R1,R2,R3,R4,R5) \in Zp
        h = hashlib.sha256()
        h.update(message.encode())
        h.update(sig.T1.to_bytes())
        h.update(sig.T2.to_bytes())
        h.update(sig.T3.to_bytes())
        h.update(R1.to_bytes())
        h.update(R2.to_bytes())
        h.update(R3.to_bytes())
        h.update(R4.to_bytes())
        h.update(R5.to_bytes())

        c = Fr.from_hash(h.digest())

        ## Compare the result with the received challenge
        if sig.c == c:
            ret["status"] = "success"
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("sig.c != c")
        return ret

    def open(self, signature: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        ## Recover the signer's A as
        sig = Signature.from_b64(signature)
        # A = T3 - (T1*xi1 + T2*xi2)
        A = sig.T3 - (
            (sig.T1 * self.manager_key.xi1) + (sig.T2 * self.manager_key.xi2)
        )
        h = hashlib.sha256(A.to_bytes())
        mem_id = h.hexdigest()
        if mem_id in self.gml:
            ret["status"] = "success"
            ret["id"] = mem_id
        return ret

```

### pygroupsig\schemes\cpy06.py

```
import hashlib
import logging
from typing import Any

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    CRL,
    GML,
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "cpy06"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    q: G1
    r: G2
    w: G2
    x: G1
    y: G1
    z: G1
    e1: GT
    e2: GT
    e3: GT
    e4: GT
    e5: GT

    def __init__(self) -> None:
        self.q = G1()  # Q \in_R G1
        self.r = G2()  # R = g2^\gamma; where g2 is G2's generator
        self.w = G2()  # W \in_R G2 \setminus 1
        self.x = G1()  # X = Z^(\xi_1^-1)
        self.y = G1()  # Y = Z^(\xi_2^-1)
        self.z = G1()  # Z \in_R G1 \setminus 1
        # Optimizations
        self.e1 = GT()  # e1 = e(g1, W). Used in sign
        self.e2 = GT()  # e2 = e(z,g2). Used in sign
        self.e3 = GT()  # e3 = e(z,r). Used in sign
        self.e4 = GT()  # e4 = e(g1,g2). Used in sign
        self.e5 = GT()  # e5 = e(q,g2). Used in verify


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    xi1: Fr
    xi2: Fr
    gamma: Fr

    def __init__(self) -> None:
        self.xi1 = Fr()  # Exponent for tracing signatures. \xi_1 \in_R Z^*_p
        self.xi2 = Fr()  # Exponent for tracing signatures. \xi_2 \in_R Z^*_p
        # Exponent for generating member keys. \gamma \in_R Z^*_p
        self.gamma = Fr()

class RevocationManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    xi1: Fr  # Revocation manager's share of ξ₁
    xi2: Fr  # Revocation manager's share of ξ₂

    def __init__(self) -> None:
        self.xi1 = Fr()  # ξ₁_rev = ξ₁ - ξ₁_group (additive share)
        self.xi2 = Fr()  # ξ₂_rev = ξ₂ - ξ₂_group (additive share)

class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    x: Fr
    t: Fr
    A: G1

    def __init__(self) -> None:
        self.x = Fr()  # x \in_R Z^*_p (non-adaptively chosen by member)
        self.t = Fr()  # t \in_R Z^*_p (chosen by manager)
        self.A = G1()  # A = (q*g_1^x)^(1/t+\gamma)


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    T1: G1
    T2: G1
    T3: G1
    T4: G2
    T5: GT
    c: Fr
    sr1: Fr
    sr2: Fr
    sd1: Fr
    sd2: Fr
    sx: Fr
    st: Fr

    def __init__(self) -> None:
        self.T1 = G1()
        self.T2 = G1()
        self.T3 = G1()
        self.T4 = G2()
        self.T5 = GT()
        self.c = Fr()
        self.sr1 = Fr()
        self.sr2 = Fr()
        self.sd1 = Fr()
        self.sd2 = Fr()
        self.sx = Fr()
        self.st = Fr()


class Group(
    JoinMixin, ReprMixin, MetadataMixin, Scheme[GroupKey, ManagerKey, MemberKey]
):
    _logger = logging.getLogger(__name__)

    _g1: G1
    _g2: G2
    gml: GML
    crl: CRL

    def __init__(self) -> None:
        self.revocation_manager_key = RevocationManagerKey()
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()
        self._g1 = G1.from_generator()
        self._g2 = G2.from_generator()
        self.gml = GML()
        self.crl = CRL()

    def setup(self) -> None:
        # \xi_1 \in_R Z^*_p
        self.manager_key.xi1.set_random()
        # \xi_2 \in_R Z^*_p
        self.manager_key.xi2.set_random()
        # \gamma \in_R Z^*_p
        self.revocation_manager_key.xi1.set_random()  # ξ₁_rev
        self.revocation_manager_key.xi2.set_random()  # ξ₂_rev
        self.manager_key.gamma.set_random()
        xi1_total = self.manager_key.xi1 + self.revocation_manager_key.xi1
        xi2_total = self.manager_key.xi2 + self.revocation_manager_key.xi2

        ## Create group public key
        # Q \in_R G1
        self.group_key.q.set_random()
        # R = g2^\gamma
        self.group_key.r.set_object(self._g2 * self.manager_key.gamma)
        # W \in_R G2 \setminus 1
        self.group_key.w.set_random()
        # Z \in_R G1 \setminus 1
        while self.group_key.z.is_zero():
            self.group_key.z.set_random()
        # X = Z*(xi_1**-1)
        self.group_key.x.set_object(self.group_key.z * ~xi1_total)
        self.group_key.y.set_object(self.group_key.z * ~xi2_total)
        ## For computation optimizations
        # e1 = e(g1, W)
        self.group_key.e1.set_object(GT.pairing(self._g1, self.group_key.w))
        # e2 = e(z,g2)
        self.group_key.e2.set_object(GT.pairing(self.group_key.z, self._g2))
        # e3 = e(z,r)
        self.group_key.e3.set_object(
            GT.pairing(self.group_key.z, self.group_key.r)
        )
        # e4 = e(g1,g2)
        self.group_key.e4.set_object(GT.pairing(self._g1, self._g2))
        # e5 = e(q,g2)
        self.group_key.e5.set_object(GT.pairing(self.group_key.q, self._g2))

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## Generate random u, v from Z^*_p
            u = Fr.from_random()
            v = Fr.from_random()

            ## Send u, v, I to member
            ret["status"] = "success"
            ret["u"] = u.to_b64()
            ret["v"] = v.to_b64()
            ret["phase"] = 1  # type: ignore
        else:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            phase = message["phase"]
            if phase == 2:
                ## Input message is <I,pi,spk>
                I_ = G1.from_b64(message["I"])
                pi = G1.from_b64(message["pi"])
                proof = spk.GeneralRepresentationProof.from_b64(message["spk"])
                Y = [pi, pi]
                G = [self._g1, I_, self.group_key.q]
                i = [
                    (0, 0),  # x*g1 (g[0],x[0])
                    (1, 0),  # v*g1 (g[0],x[1])
                    (2, 1),  # u*I (g[1],x[2])
                    (3, 2),  # rr*q (g[2],x[3])
                ]
                prods = [1, 3]

                if spk.general_representation_verify(
                    Y, G, i, prods, proof, pi.to_bytes()
                ):
                    # t \in_R Z^*_p
                    t = Fr.from_random()
                    # A = (pi+q) * ((gamma+t)**-1)
                    A = (pi + self.group_key.q) * ~(self.manager_key.gamma + t)

                    ## Update the gml
                    h = hashlib.sha256()
                    h.update(A.to_bytes())
                    h.update(pi.to_bytes())
                    self.gml[h.hexdigest()] = (A, pi)

                    ## Write the partial memkey into mout
                    ret["status"] = "success"
                    ret["t"] = t.to_b64()
                    ret["A"] = A.to_b64()
                    ret["phase"] = phase + 1
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("spk.rep_verify failed")
            else:
                ret["message"] = (
                    f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
                )
                self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, Any]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## Read u and v from input message
            u = Fr.from_b64(message["u"])
            v = Fr.from_b64(message["v"])

            ## Commit to randomness
            # y,r \in_R Z^*_p
            y = Fr.from_random()
            r = Fr.from_random()

            # I = yG1 + rQ
            e = (G1 * 2)()
            e[0].set_object(self._g1)
            e[1].set_object(self.group_key.q)
            s = (Fr * 2)()
            s[0].set_object(y)
            s[1].set_object(r)
            I_ = G1.muln(e, s)

            ## memkey->x = u*memkey->y + v
            member_key.x.set_object((u * y) + v)

            ## pi = G1*xi
            pi = self._g1 * member_key.x

            ## rr = -r' = -u*r
            rr = -(u * r)

            ## We'll be signing pi in the SPK
            Y = [pi, pi]
            G = [self._g1, I_, self.group_key.q]
            x = [member_key.x, v, u, rr]
            i = [
                (0, 0),  # x*g1 (g[0],x[0])
                (1, 0),  # v*g1 (g[0],x[1])
                (2, 1),  # u*I (g[1],x[2])
                (3, 2),  # rr*q (g[2],x[3])
            ]
            prods = [1, 3]

            proof = spk.general_representation_sign(
                Y, G, x, i, prods, pi.to_bytes()
            )
            ret["status"] = "success"
            ret["I"] = I_.to_b64()
            ret["pi"] = pi.to_b64()
            ret["spk"] = proof.to_b64()
            ret["phase"] = phase + 1
        elif phase == 3:
            ## Import partial key from message
            t_ = Fr.from_b64(message["t"])
            A_ = G1.from_b64(message["A"])
            aux_g2 = (self._g2 * t_) + self.group_key.r
            aux_g1 = (self._g1 * member_key.x) + self.group_key.q

            aux_gt1 = GT.pairing(A_, aux_g2)
            aux_gt2 = GT.pairing(aux_g1, self._g2)
            if aux_gt1 == aux_gt2:
                ## All good: transfer all data to memkey
                member_key.t.set_object(t_)
                member_key.A.set_object(A_)
                ret["status"] = "success"
            else:
                ret["status"] = "fail"
                ret["message"] = "Invalid message content"
                self._logger.debug("aux_gt1 != aux_gt2")
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def sign(self, message: str, member_key: MemberKey) -> dict[str, Any]:
        message = str(message)

        # r1,r2,r3 \in_R Z_p
        r1 = Fr.from_random()
        r2 = Fr.from_random()
        r3 = Fr.from_random()
        # d1 = t*r1
        d1 = member_key.t * r1
        # d2 = t*r2
        d2 = member_key.t * r2

        sig = Signature()
        # T1 = X*r1
        sig.T1.set_object(self.group_key.x * r1)
        # T2 = Y*r2
        sig.T2.set_object(self.group_key.y * r2)
        # T3 = A + Z*(r1+r2)
        sig.T3.set_object(member_key.A + (self.group_key.z * (r1 + r2)))
        # T4 = W*r3
        sig.T4.set_object(self.group_key.w * r3)
        ## e(...) precalculated in setup
        # T5 = e(g1, T4)**x = e(g1, W)**(r3*x)
        sig.T5.set_object(self.group_key.e1 ** (r3 * member_key.x))

        # br1, br2,bd1,bd2,bt,bx \in_R Z_p
        br1 = Fr.from_random()
        br2 = Fr.from_random()
        bd1 = Fr.from_random()
        bd2 = Fr.from_random()
        bt = Fr.from_random()
        bx = Fr.from_random()

        # B1 = X*br1
        B1 = self.group_key.x * br1
        # B2 = Y*br2
        B2 = self.group_key.y * br2
        # B3 = T1*bt - X*bd1
        B3 = (sig.T1 * bt) - (self.group_key.x * bd1)
        # B4 = T2*bt - Y*bd2
        B4 = (sig.T2 * bt) - (self.group_key.y * bd2)
        # B5 = e(g1,T4)^bx
        B5 = GT.pairing(self._g1, sig.T4) ** bx
        # B6 = e(T3,g2)^bt * e(z,g2)^(-bd1-bd2) * e(z,r)^(-br1-br2) * e(g1,g2)^(-bx)
        B6 = GT.pairing(sig.T3, self._g2) ** bt

        ## aux_e: the rest (with the help of the optimizations is easier...)
        e = (GT * 3)()
        e[0].set_object(self.group_key.e2)
        e[1].set_object(self.group_key.e3)
        e[2].set_object(self.group_key.e4)
        s = (Fr * 3)()
        s[0].set_object((-bd1) - bd2)
        s[1].set_object((-br1) - br2)
        s[2].set_object(-bx)
        aux_e = GT.pown(e, s)
        B6 = B6 * aux_e

        # c = hash(M,T1,T2,T3,T4,T5,B1,B2,B3,B4,B5,B6) \in Zp
        h = hashlib.sha256()
        h.update(message.encode())
        h.update(sig.T1.to_bytes())
        h.update(sig.T2.to_bytes())
        h.update(sig.T3.to_bytes())
        h.update(sig.T4.to_bytes())
        h.update(sig.T5.to_bytes())
        h.update(B1.to_bytes())
        h.update(B2.to_bytes())
        h.update(B3.to_bytes())
        h.update(B4.to_bytes())
        h.update(B5.to_bytes())
        h.update(B6.to_bytes())
        sig.c.set_hash(h.digest())

        # sr1 = br1 + c*r1
        sig.sr1.set_object(br1 + (sig.c * r1))
        # sr2 = br2 + c*r2
        sig.sr2.set_object(br2 + (sig.c * r2))
        # sd1 = bd1 + c*d1
        sig.sd1.set_object(bd1 + (sig.c * d1))
        # sd2 = bd2 + c*d2
        sig.sd2.set_object(bd2 + (sig.c * d2))
        # sx = bx + c*x
        sig.sx.set_object(bx + (sig.c * member_key.x))
        # st = bt + c*t
        sig.st.set_object(bt + (sig.c * member_key.t))
        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(self, message: str, signature: str) -> dict[str, Any]:
        message = str(message)
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)

        ## Re-derive B1, B2, B3, B4, B5 and B6 from the signature
        # B1 = X*sr1 - T1*c
        B1 = (self.group_key.x * sig.sr1) - (sig.T1 * sig.c)
        # B2 = X*sr2 - T2*c
        B2 = (self.group_key.y * sig.sr2) - (sig.T2 * sig.c)
        # B3 = T1*st - X*sd1
        B3 = (sig.T1 * sig.st) - (self.group_key.x * sig.sd1)
        # B4 = T2*st - Y*sd2
        B4 = (sig.T2 * sig.st) - (self.group_key.y * sig.sd2)
        # B5 = e(g1,T4)**sx * T5**-c
        B5 = ((GT.pairing(self._g1, sig.T4)) ** sig.sx) * ~(sig.T5**sig.c)
        # B6 = e(T3,g2)^st * e(z,g2)^(-sd1-sd2) * e(z,r)^(-sr1-sr2) * e(g1,g2)^(-sx) * ( e(T3,r)/e(q,g2) )^c
        # aux_e = e(z,g2)^(-sd1-sd2) * e(z,r)^(-sr1-sr2) * e(g1,g2)^(-sx)
        e = (GT * 3)()
        e[0].set_object(self.group_key.e2)
        e[1].set_object(self.group_key.e3)
        e[2].set_object(self.group_key.e4)
        s = (Fr * 3)()
        s[0].set_object((-sig.sd1) - sig.sd2)
        s[1].set_object((-sig.sr1) - sig.sr2)
        s[2].set_object(-sig.sx)
        aux_e = GT.pown(e, s)

        # aux_GT = (e(T3,r) / e(q,g2))**c
        aux_GT = (
            (GT.pairing(sig.T3, self.group_key.r)) / self.group_key.e5
        ) ** sig.c

        # B6 = e(T3,g2)^st * aux_e * aux_GT
        B6 = (GT.pairing(sig.T3, self._g2)) ** sig.st * aux_e * aux_GT

        ## Recompute the hash-challenge c
        h = hashlib.sha256()
        h.update(message.encode())
        h.update(sig.T1.to_bytes())
        h.update(sig.T2.to_bytes())
        h.update(sig.T3.to_bytes())
        h.update(sig.T4.to_bytes())
        h.update(sig.T5.to_bytes())
        h.update(B1.to_bytes())
        h.update(B2.to_bytes())
        h.update(B3.to_bytes())
        h.update(B4.to_bytes())
        h.update(B5.to_bytes())
        h.update(B6.to_bytes())
        c = Fr.from_hash(h.digest())

        ## Compare the result with the received challenge
        if c == sig.c:
            ret["status"] = "success"
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("c != sig.c")
        return ret

    def open(self, signature: str,group_manager_partial: dict[str, Any] = None, revocation_manager_partial: dict[str, Any] = None) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        ## Recover the signer's A as: A = T3-(T1*xi1 + T2*xi2)
        # A = T1*xi1 + T2*xi2 =
        if group_manager_partial is None:
        # Group Manager computes partial decryption
            partial_g = {
                "T1_xi": self.manager_key.xi1,
                "T2_xi": self.manager_key.xi2
            }
            return {"status": "partial", "partial_g": partial_g}
        elif revocation_manager_partial is None:
        # Revocation Manager computes partial decryption
            partial_r = {
                "T1_xi": self.revocation_manager_key.xi1,
                "T2_xi": self.revocation_manager_key.xi2
            }
            return {"status": "partial", "partial_r": partial_r}
        else:
            xi1_total = group_manager_partial["T1_xi"] + revocation_manager_partial["T1_xi"]
            xi2_total = group_manager_partial["T2_xi"] + revocation_manager_partial["T2_xi"]

            e = (G1 * 2)()
            e[0].set_object(sig.T1)
            e[1].set_object(sig.T2)
            s = (Fr * 2)()
            s[0].set_object(xi1_total)
            s[1].set_object(xi2_total)
            A = G1.muln(e, s)
            A = sig.T3 - A

        # Lookup A in GML
            for mem_id, (open_trap, _) in self.gml.items():
                if A == open_trap:
                    return {"status": "success", "id": mem_id}
            return {"status": "fail"}
            

    def reveal(self, member_id: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        if member_id in self.gml:
            ret["status"] = "success"
            self.crl[member_id] = self.gml[member_id]
        return ret

    def trace(self, signature: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        for _, (_, trace_trap) in self.crl.items():
            e = GT.pairing(trace_trap, sig.T4)
            if e == sig.T5:
                ret["status"] = "success"
                ret["revoked"] = True  # type: ignore
                break
        return ret

    def prove_equality(
        self, signatures: list[str], member_key: MemberKey
    ) -> dict[str, Any]:
        ## Initialize the hashing environment
        h = hashlib.sha256()
        ## Get random r
        r = Fr.from_random()
        ## To create the proof, we make use of the T4 and T5 objects of the signatures.
        ## The knowledge of the discrete logarithm of T5 to the base e(g1,T4) is used in
        ## normal signature claims. In the same way, given two signatures (allegedly)
        ## issued by the same member, with corresponding objects T4, T5, T4' and T5', we
        ## prove here that the discrete logarithm of T5 to the base e(g1,T4) is the same
        ## to that of T5' to the base e(g1,T4')

        ## (1) Raise e(g1,T4) of each received signature to r, and put it into the hash
        for s in signatures:
            sig = Signature.from_b64(s)
            e = GT.pairing(self._g1, sig.T4)
            er = e**r
            ## Put the i-th e(g1,T4)^r element of the array
            h.update(er.to_bytes())
            ## Put the also the base ( = e(g1,T4) ) into the hash
            h.update(e.to_bytes())
            ## ... and T5
            h.update(sig.T5.to_bytes())

        proof = spk.NizkProof()
        ## (2) Calculate c = hash((e(g1,T4)^r)[1] || (e(g1,T4))[1] || ... ||
            ## (e(g1,T4)^r)[n] || (e(g1,T4))[n] )
        proof.c.set_hash(h.digest())
        ## (3) To end, get s = r - c*x
        proof.s.set_object(r + (proof.c * member_key.x))
        return {
            "status": "success",
            "proof": proof.to_b64(),
        }

    def prove_equality_verify(
        self, signatures: list[str], proof: str
    ) -> dict[str, Any]:
        ret = {"status": "fail"}
        ## Initialize the hashing environment
        h = hashlib.sha256()
        ## We have to recover the e(g1,T4)^r objects. To do so,
        ## we divide e(g1,T4)^s/T5^c
        proof_ = spk.NizkProof.from_b64(proof)
        for s in signatures:
            sig = Signature.from_b64(s)
            e = GT.pairing(self._g1, sig.T4)
            es = (e**proof_.s) / (sig.T5**proof_.c)
            ## Put the i-th element of the array
            h.update(es.to_bytes())
            ## Put also the base (the e(g1,T4)'s) into the hash
            h.update(e.to_bytes())
            ## ... and T5
            h.update(sig.T5.to_bytes())

        ## (2) Calculate c = hash((e(g1,T4)^r)[1] || (e(g1,T4))[1] || ... ||
        ## (e(g1,T4)^r)[n] || (e(g1,T4))[n] )
        ## Now, we have to get c as an element
        c = Fr.from_hash(h.digest())
        if c == proof_.c:
            ret["status"] = "success"
        else:
            ret["message"] = "Invalid proof"
            self._logger.debug("c != proof_.c")
        return ret

    def claim(self, signature: str, member_key: MemberKey) -> dict[str, Any]:
        ## A claim is just similar to proving "equality" of N sigature, but just
        ## for 1 signature
        return self.prove_equality([signature], member_key)

    def claim_verify(self, signature: str, proof: str) -> dict[str, Any]:
        ## A claim verification is just similar to proving "equality verification" of N sigature, but just
        ## for 1 signature
        return self.prove_equality_verify([signature], proof)

```

### pygroupsig\schemes\dl21.py

```
import hashlib
import logging
from typing import Any, Generic, Type, TypeVar

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "dl21"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    g1: G1
    g2: G2
    h1: G1
    h2: G1
    ipk: G2

    def __init__(self) -> None:
        self.g1 = G1()  # Params. Random generator of G1
        self.g2 = G2()  # Params. Random generator of G2
        self.h1 = G1()  # Params. Random generator of G1
        self.h2 = G1()  # Params. Random generator of G1
        self.ipk = G2()  # Isseur public key


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    isk: Fr

    def __init__(self) -> None:
        self.isk = Fr()  # Issuer secret key


class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    A: G1
    x: Fr
    y: Fr
    s: Fr
    H: G1
    h2s: G1

    def __init__(self) -> None:
        self.A = G1()  # A = (H*h2^s*g1)^(1/isk+x)
        self.x = Fr()  # Randomly picked by the Issuer
        self.y = Fr()  # Randomly picked by the Member
        self.s = Fr()  # Randomly picked by the Issuer
        self.H = G1()  # Member's "public key". H = h1^y
        self.h2s = G1()  # Used in signatures. h2s = h2^s


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    AA: G1
    A_: G1
    d: G1
    pi: spk.GeneralRepresentationProof
    nym: G1

    def __init__(self) -> None:
        self.AA = G1()
        self.A_ = G1()
        self.d = G1()
        self.pi = spk.GeneralRepresentationProof()
        self.nym = G1()


SignatureT = TypeVar("SignatureT", bound=Signature)


class Group(
    JoinMixin,
    ReprMixin,
    MetadataMixin,
    Generic[SignatureT],
    Scheme[GroupKey, ManagerKey, MemberKey],
):
    _logger = logging.getLogger(__name__)

    def __init__(self) -> None:
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()

    def setup(self) -> None:
        ## Initialize the manager key
        self.manager_key.isk.set_random()

        ## Initialize the group key
        self.group_key.g1.set_random()
        self.group_key.h1.set_random()
        self.group_key.h2.set_random()

        ## Compute random generator g2 in G2. Since G2 is a cyclic group of prime
        ## order, just pick a random element
        self.group_key.g2.set_random()

        ## Set the Issuer public key
        self.group_key.ipk.set_object(self.group_key.g2 * self.manager_key.isk)

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## Send a random element to the member
            n = G1.from_random()
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["phase"] = 1  # type: ignore
            ## TODO: This value should be saved in some place to avoid replay attack
        else:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            phase = message["phase"]
            if phase == 2:
                ## Second step by manager: compute credential from H and pi_H */
                ## Verify the proof
                n = G1.from_b64(message["n"])
                H = G1.from_b64(message["H"])
                proof = spk.DiscreteLogProof.from_b64(message["pi"])
                if spk.discrete_log_verify(
                    H, self.group_key.h1, proof, n.to_bytes()
                ):
                    ## Pick x and s at random from Z*_p
                    x = Fr.from_random()
                    s = Fr.from_random()

                    # Set A = (H+h_2*s+g_1)*((isk+x)**-1)
                    A = (H + (self.group_key.h2 * s) + self.group_key.g1) * ~(
                        self.manager_key.isk + x
                    )

                    ## Mout = (A,x,s)
                    ## This is stored in a partially filled memkey, byte encoded into a
                    ## message_t struct
                    ret["status"] = "success"
                    ret["x"] = x.to_b64()
                    ret["s"] = s.to_b64()
                    ret["A"] = A.to_b64()
                    ret["phase"] = phase + 1
                else:
                    ret["message"] = "Invalid message content"
                    self._logger.debug("spk.dlog_G1_verify failed")
            else:
                ret["message"] = (
                    f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
                )
                self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, Any]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## First step by the member: parse n and compute (Y,\pi_Y)
            n = G1.from_b64(message["n"])

            ## Compute member's secret key y at random
            member_key.y.set_random()

            ## Compute the member's public key
            member_key.H.set_object(self.group_key.h1 * member_key.y)

            ## Compute the SPK
            proof = spk.discrete_log_sign(
                member_key.H, self.group_key.h1, member_key.y, n.to_bytes()
            )

            ## Build the output message
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["H"] = member_key.H.to_b64()
            ret["pi"] = proof.to_b64()
            ret["phase"] = phase + 1
        elif phase == 3:
            ## Second step by the member: Check correctness of computation
            ## and update memkey
            member_key.x.set_b64(message["x"])
            member_key.s.set_b64(message["s"])
            member_key.A.set_b64(message["A"])

            ## Recompute h2s from s
            member_key.h2s.set_object(self.group_key.h2 * member_key.s)

            ## Check correctness

            ## A must not be 1 (since we use additive notation for G1,
            ## it must not be 0)
            if not member_key.A.is_zero():
                # Check correctness: e(v,gg) = e(u,XX)e(w,YY)
                e1 = (
                    GT.pairing(member_key.A, self.group_key.g2)
                ) ** member_key.x
                e2 = GT.pairing(member_key.A, self.group_key.ipk)
                e4 = e1 * e2
                aux = (member_key.h2s + member_key.H) + self.group_key.g1
                e3 = GT.pairing(aux, self.group_key.g2)
                if e4 == e3:
                    ret["status"] = "success"
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("e4 != e3")
            else:
                ret["status"] = "fail"
                ret["message"] = "Invalid message content"
                self._logger.debug("key.A is zero")
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    _scheme_signature: Type[SignatureT] = Signature  # type: ignore

    def sign(
        self, message: str, member_key: MemberKey, scope: str = "def"
    ) -> dict[str, Any]:
        sig = self._common_sign(message, member_key, scope)
        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def _common_sign(
        self, message: str, member_key: MemberKey, scope: str
    ) -> SignatureT:
        message = str(message)
        scope = str(scope)
        # r1, r2 \in_R Z_p
        r1 = Fr.from_random()
        r2 = Fr.from_random()

        sig = self._scheme_signature()
        # nym = Hash(scp)*y
        h = hashlib.sha256(scope.encode())
        hscp = G1.from_hash(h.digest())
        sig.nym.set_object(hscp * member_key.y)

        # AA = A*r1
        sig.AA.set_object(member_key.A * r1)

        ## Good thing we precomputed much of this...
        # aux = (g1*h1^y*h2^s)^r1
        aux = (self.group_key.g1 + member_key.H + member_key.h2s) * r1
        # A_ = AA*-x+(g1+h1*y+h2*s)*r1
        sig.A_.set_object((sig.AA * -member_key.x) + aux)

        # d = (g1+h1*y+h2*s)*r1+h2*-r2
        sig.d.set_object(aux + (self.group_key.h2 * -r2))

        # r3 = r1**-1
        r3 = ~r1

        # ss = s - r2*r3
        ss = member_key.s - (r2 * r3)
        ## Auxiliar variables for the spk
        aux_Zr = -member_key.x
        ss = -ss
        negy = -member_key.y
        A_d = sig.A_ - sig.d

        y = [sig.nym, A_d, self.group_key.g1]
        g = [hscp, sig.AA, self.group_key.h2, sig.d, self.group_key.h1]
        x = [aux_Zr, member_key.y, r2, r3, ss, negy]
        i = [
            (1, 0),  # hscp^y = (g[0],x[1])
            (0, 1),  # AA^-x = (g[1],x[0])
            (2, 2),  # h2^r2 = (g[2],x[2])
            (3, 3),  # d^r3 = (g[3],x[3])
            (4, 2),  # h2^-ss = (g[2],x[4])
            (5, 4),  # h1^-y = (g[4],x[5])
        ]
        prods = [1, 2, 3]
        proof = spk.general_representation_sign(y, g, x, i, prods, message)
        sig.pi.c.set_object(proof.c)
        sig.pi.s.extend(proof.s)
        return sig

    def verify(
        self, message: str, signature: str, scope: str = "def"
    ) -> dict[str, Any]:
        message = str(message)
        scope = str(scope)
        ret = {"status": "fail"}
        sig = self._scheme_signature.from_b64(signature)
        ## AA must not be 1 (since we use additive notation for G1,
        ## it must not be 0)
        if not sig.AA.is_zero():
            ## e(AA,ipk) must equal e(A_,g2)
            e1 = GT.pairing(sig.AA, self.group_key.ipk)
            e2 = GT.pairing(sig.A_, self.group_key.g2)
            if e1 == e2:
                ## Recompute hscp
                h = hashlib.sha256(scope.encode())
                hscp = G1.from_hash(h.digest())
                A_d = sig.A_ - sig.d
                y = [sig.nym, A_d, self.group_key.g1]
                g = [hscp, sig.AA, self.group_key.h2, sig.d, self.group_key.h1]
                i = [
                    (1, 0),  # hscp^y = (g[0],x[1])
                    (0, 1),  # AA^-x = (g[1],x[0])
                    (2, 2),  # h2^r2 = (g[2],x[2])
                    (3, 3),  # d^r3 = (g[3],x[3])
                    (4, 2),  # h2^-ss = (g[2],x[4])
                    (5, 4),  # h1^-y = (g[4],x[5])
                ]
                prods = [1, 2, 3]
                ## Verify SPK
                if spk.general_representation_verify(
                    y, g, i, prods, sig.pi, message
                ):
                    ret["status"] = "success"
                else:
                    ret["message"] = "Invalid signature"
                    self._logger.debug("spk.rep_verify failed")
            else:
                ret["message"] = "Invalid signature"
                self._logger.debug("e1 != e2")
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("AA is zero")
        return ret

    # noinspection PyUnresolvedReferences
    def identify(
        self, signature: str, member_key: MemberKey, scope: str = "def"
    ) -> dict[str, Any]:
        scope = str(scope)
        ret = {"status": "fail"}
        sig = self._scheme_signature.from_b64(signature)
        ## Recompute nym
        h = hashlib.sha256()
        h.update(scope.encode())
        hscp = G1.from_hash(h.digest())
        nym = hscp * member_key.y
        ## Check if nym = h(scp)*y
        if nym == sig.nym:
            ret["status"] = "success"
        return ret

    def link(
        self,
        message: str,
        messages: list[str],
        signatures: list[str],
        member_key: MemberKey,
        scope: str = "def",
    ) -> dict[str, Any]:
        scope = str(scope)
        ret = {"status": "fail"}
        hscp = G1()
        for msg, sig_b64 in zip(messages, signatures):
            ## Verify signature
            ver_msg = self.verify(msg, sig_b64, scope=scope)
            ## Check if it is a signature issued by memkey
            iden_msg = self.identify(sig_b64, member_key, scope=scope)
            h = hashlib.sha256()
            if (
                ver_msg["status"] == "success"
                and iden_msg["status"] == "success"
            ):
                h.update(scope.encode())
                ## "Accumulate" scp
                hscp += G1.from_hash(h.digest())
            else:
                if ver_msg["status"] != "success":
                    ret["message1"] = "Invalid messages/signatures"
                    self._logger.debug("Signature verify failed")
                if iden_msg["status"] != "success":
                    ret["message2"] = "Invalid messages/signatures"
                    self._logger.debug("Signature identify failed")
                break
        else:
            # nym_ = hscp * y
            nym = hscp * member_key.y
            ## Do the SPK
            proof = spk.discrete_log_sign(nym, hscp, member_key.y, message)
            ret["status"] = "success"
            ret["proof"] = proof.to_b64()
        return ret

    # noinspection PyUnresolvedReferences
    def link_verify(
        self,
        message: str,
        messages: list[str],
        signatures: list[str],
        proof: str,
        scope: str = "def",
    ) -> dict[str, Any]:
        scope = str(scope)
        ret = {"status": "fail"}
        proof_ = spk.DiscreteLogProof.from_b64(proof)
        hscp = G1()
        nym = G1()
        for msg, sig_b64 in zip(messages, signatures):
            ver_msg = self.verify(msg, sig_b64)
            if ver_msg["status"] == "success":
                h = hashlib.sha256()
                h.update(scope.encode())
                ## "Accumulate" scp
                hscp += G1.from_hash(h.digest())
                ## "Accumulate" nym
                sig = self._scheme_signature.from_b64(sig_b64)
                nym += sig.nym
            else:
                ret["message"] = "Invalid messages/signatures"
                self._logger.debug("Signature verify failed")
                break
        else:
            if spk.discrete_log_verify(nym, hscp, proof_, message):
                ret["status"] = "success"
            else:
                ret["message"] = "Invalid proof"
                self._logger.debug("spk.dlog_G1_verify failed")
        return ret

```

### pygroupsig\schemes\dl21seq.py

```
import hashlib
import hmac
import logging
from typing import Any, Type, TypeVar

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container
from pygroupsig.schemes.dl21 import Group as GroupDL21
from pygroupsig.schemes.dl21 import GroupKey as GroupKeyDL21
from pygroupsig.schemes.dl21 import ManagerKey as ManagerKeyDL21
from pygroupsig.schemes.dl21 import MemberKey as MemberKeyDL21
from pygroupsig.schemes.dl21 import Signature as SignatureDL21
from pygroupsig.utils.mcl import G1, GT, Fr


class MetadataMixin:
    _name = "dl21seq"


T = TypeVar("T", bound=Container)
U = TypeVar("U")


class FromDL21Mixin:
    @classmethod
    def from_dl21(cls: Type[T], o: U) -> T:  # type: ignore
        ret = cls()
        for v in vars(o):
            s_obj = getattr(o, v)
            d_obj = getattr(ret, v)
            d_obj.set_object(s_obj)
        return ret


class GroupKey(FromDL21Mixin, GroupKeyDL21):
    pass


class ManagerKey(FromDL21Mixin, ManagerKeyDL21):
    pass


class MemberKey(FromDL21Mixin, MemberKeyDL21):
    k: str
    kk: str

    def __init__(self) -> None:
        super().__init__()
        self.k = ""
        self.kk = ""


class Signature(FromDL21Mixin, SignatureDL21):
    seq: dict[str, str]

    def __init__(self) -> None:
        super().__init__()
        ## seq(1): Computed as Hash(k',PRF(k,seq3)),
        ## seq(2): Computed as Hash(k',PRF(k,seq3) xor Hash(k, PRF(k,i-1)))
        ## seq(3): Computed as PRF(k,i) -- converted to byte
        self.seq = {}


class Group(GroupDL21[Signature]):
    _logger = logging.getLogger(__name__)

    def __init__(self) -> None:
        super().__init__()
        self.group_key = GroupKey.from_dl21(self.group_key)
        self.manager_key = ManagerKey.from_dl21(self.manager_key)

    ## init, setup, join_mgr, join_mem* are the same as Dl21
    ## join_mem initializes k and k' in the last phase
    def join_mem(  # type: ignore
        self,
        message: dict[str, Any],
        member_key: MemberKey,
    ) -> dict[str, Any]:
        ret = super().join_mem(message, member_key)
        if message["phase"] == 3:
            member_key.k = hashlib.sha256(
                Fr.from_random().to_bytes()
            ).hexdigest()
            member_key.kk = hashlib.sha256(
                Fr.from_random().to_bytes()
            ).hexdigest()
        return ret

    _scheme_signature: Type[Signature] = Signature

    def sign(  # type: ignore
        self,
        message: str,
        member_key: MemberKey,
        scope: str = "def",
        state: int = 0,
    ) -> dict[str, Any]:
        # sign is partially the same
        sig = self._common_sign(message, member_key, scope)
        ## Compute seq3 = PRF(k,state)
        sig.seq["3"] = prf_compute(member_key.k, state)

        ## Compute x_i = PRF(k',state)
        xi = prf_compute(member_key.kk, sig.seq["3"])
        xi_b = bytes.fromhex(xi)

        # seq1 = Hash(x_i)
        sig.seq["1"] = hashlib.sha256(xi_b).hexdigest()

        ## Compute x_{i-1} = PRF(k',PRF(k,state-1))
        ## Recompute n_{i-1} = PRF(k,state-1)
        ni1 = prf_compute(member_key.k, state - 1)
        xi1 = prf_compute(member_key.kk, ni1)
        xi1_b = bytes.fromhex(xi1)
        # seq2 = Hash(x_i \xor x_{i-1})
        _xi = bytearray(a ^ b for a, b in zip(xi_b, xi1_b))
        sig.seq["2"] = hashlib.sha256(_xi).hexdigest()
        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(
        self, message: str, signature: str, scope: str = "def"
    ) -> dict[str, Any]:
        message = str(message)
        scope = str(scope)
        ret = {"status": "fail"}
        sig = self._scheme_signature.from_b64(signature)
        ## AA must not be 1 (since we use additive notation for G1,
        ## it must not be 0)
        if not sig.AA.is_zero():
            ## e(AA,ipk) must equal e(A_,g2)
            e1 = GT.pairing(sig.AA, self.group_key.ipk)
            e2 = GT.pairing(sig.A_, self.group_key.g2)
            if e1 == e2:
                ## Recompute hscp
                hc = hashlib.sha256(scope.encode()).digest()
                hscp = G1.from_hash(hc)
                A_d = sig.A_ - sig.d
                y = [sig.nym, A_d, self.group_key.g1]
                g = [hscp, sig.AA, self.group_key.h2, sig.d, self.group_key.h1]
                i = [
                    (1, 0),  # hscp^y = (g[0],x[1])
                    (0, 1),  # AA^-x = (g[1],x[0])
                    (2, 2),  # h2^r2 = (g[2],x[2])
                    (3, 3),  # d^r3 = (g[3],x[3])
                    (4, 2),  # h2^-ss = (g[2],x[4])
                    (5, 4),  # h1^-y = (g[4],x[5])
                ]
                prods = [1, 2, 3]
                ## Verify SPK
                if spk.general_representation_verify(
                    y, g, i, prods, sig.pi, message
                ):
                    ret["status"] = "success"
                else:
                    ret["message"] = "Invalid signature"
                    self._logger.debug("spk.rep_verify failed")
            else:
                ret["message"] = "Invalid signature"
                self._logger.debug("e1 != e2")
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("AA is zero")
        return ret

    def seqlink(
        self,
        message: str,
        messages: list[str],
        signatures: list[str],
        key: MemberKey,
        scope: str = "def",
    ) -> dict[str, Any]:
        ret = self.link(message, messages, signatures, key, scope)
        _proof = spk.DiscreteLogProof2.from_b64(ret["proof"])
        ## x[i] = PRF(k',n[i]) = PRF(k',seq3[i])
        for sig_b64 in signatures:
            sig = self._scheme_signature.from_b64(sig_b64)
            _proof.x.append(prf_compute(key.kk, sig.seq["3"]))
        ret["proof"] = _proof.to_b64()
        return ret

    def seqlink_verify(
        self,
        message: str,
        messages: list[str],
        signatures: list[str],
        proof: str,
        scope: str = "def",
    ) -> dict[str, str]:
        _proof2 = spk.DiscreteLogProof2.from_b64(proof)
        _proof = spk.DiscreteLogProof()
        _proof.c.set_object(_proof2.c)
        _proof.s.set_object(_proof2.s)
        ret = self.link_verify(
            message, messages, signatures, _proof.to_b64(), scope
        )
        ## Iterate through sigs and check that
        ## sig[i]->seq1 = Hash(x[i]) and sig[i]->seq2 = Hash(x[i] xor x[i-1])
        for idx, sig_b64 in enumerate(signatures):
            sig = self._scheme_signature.from_b64(sig_b64)
            _hash = hashlib.sha256(bytes.fromhex(_proof2.x[idx])).hexdigest()
            if _hash == sig.seq["1"]:
                if idx > 0:
                    xi1_b = bytes.fromhex(_proof2.x[idx - 1])
                    xi_b = bytes.fromhex(_proof2.x[idx])
                    # seq2 = Hash(x_i \xor x_{i-1})
                    _xi = bytearray(a ^ b for a, b in zip(xi1_b, xi_b))
                    _hash2 = hashlib.sha256(_xi).hexdigest()
                    if _hash2 != sig.seq["2"]:
                        ret["status"] = "fail"
                        ret["message"] = "Invalid signature sequence"
                        self._logger.debug("_hash2 != seq2")
            else:
                ret["status"] = "fail"
                ret["message"] = "Invalid signature sequence"
                self._logger.debug("_hash != seq1")
                break
        return ret


def prf_compute(key: str, state: int | str) -> str:
    return hmac.new(
        bytes.fromhex(key), str(state).encode(), hashlib.sha256
    ).hexdigest()

```

### pygroupsig\schemes\gl19.py

```
import hashlib
import logging
import random
import time
from typing import Any, Type

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "gl19"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    g1: G1
    g2: G2
    g: G1
    h: G1
    h1: G1
    h2: G1
    h3: G1
    ipk: G2
    cpk: G1
    epk: G1

    def __init__(self) -> None:
        self.g1 = G1()  # Random generator of G1
        self.g2 = G2()  # Random generator of G2
        self.g = G1()  # Random generator of G1
        self.h = G1()  # Random generator of G1
        self.h1 = G1()  # Random generator of G1
        self.h2 = G1()  # Random generator of G1
        self.h3 = G1()  # Random generator of G1. Used for setting expiration date of member creds
        self.ipk = G2()  # Issuer public key
        self.cpk = G1()  # Converter public key
        self.epk = G1()  # Extractor public key


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    isk: Fr
    csk: Fr
    esk: Fr

    def __init__(self) -> None:
        self.isk = Fr()  # Issuer secret key
        self.csk = Fr()  # Converter secret key
        self.esk = Fr()  # Extractor secret key


class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    A: G1
    x: Fr
    y: Fr
    s: Fr
    l: int
    d: Fr
    H: G1
    h2s: G1
    h3d: G1

    def __init__(self) -> None:
        self.A = G1()  # A = (H*h2^s*g1)^(1/isk+x)
        self.x = Fr()  # Randomly picked by the Issuer
        self.y = Fr()  # Randomly picked by the Member
        self.s = Fr()  # Randomly picked by the Issuer
        self.l = -1  # Lifetime of the credential (UNIX time seconds)
        self.d = Fr()  # Fr element mapped from Hash(lifetime)
        self.H = G1()  # Member's "public key". H = h1^y
        self.h2s = G1()  # Used in signatures. h2s = h2^s
        self.h3d = G1()  # Used in signatures. h3d = h3^d


class BlindKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    pk: G1
    sk: Fr

    def __init__(self) -> None:
        self.pk = G1()  # Public key. Equals g^sk
        self.sk = Fr()  # Randomly chosen private key

    @classmethod
    def from_random(cls: Type["BlindKey"], grpkey: GroupKey) -> "BlindKey":
        ret = cls()
        ret.sk.set_random()
        ret.pk.set_object(grpkey.g * ret.sk)
        return ret

    def public(self) -> str:
        return self.pk.to_b64()


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    AA: G1
    A_: G1
    d: G1
    pi: spk.GeneralRepresentationProof
    nym1: G1
    nym2: G1
    ehy1: G1
    ehy2: G1
    expiration: int

    def __init__(self) -> None:
        self.AA = G1()
        self.A_ = G1()
        self.d = G1()
        self.pi = spk.GeneralRepresentationProof()
        self.nym1 = G1()
        self.nym2 = G1()
        self.ehy1 = G1()
        self.ehy2 = G1()
        self.expiration = -1
        # Expiration date. This is metainformation actually
        # pertaining to the signer's credential. The verify
        # process checks that the signature was produced by a
        # signer controlling a credential with the corresponding
        # expiration date


class BlindSignature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    nym1: G1
    nym2: G1
    nym3: G1
    c1: G1
    c2: G1

    def __init__(self) -> None:
        self.nym1 = G1()
        self.nym2 = G1()
        self.nym3 = G1()
        self.c1 = G1()
        self.c2 = G1()


class Group(
    JoinMixin, ReprMixin, MetadataMixin, Scheme[GroupKey, ManagerKey, MemberKey]
):
    LIFETIME: int = 60 * 60 * 24 * 14  # two weeks
    # TODO: add lifetime setter
    _logger = logging.getLogger(__name__)

    def __init__(self) -> None:
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()

    def setup(self) -> None:
        ## Initializes the Manager key
        self.manager_key.isk.set_random()

        ## Initializes the Group key
        # Compute random generators g1, g, h, h1 and h2 in G1. Since G1 is a cyclic
        # group of prime order, just pick random elements
        self.group_key.g1.set_random()
        self.group_key.g.set_random()
        self.group_key.h.set_random()
        self.group_key.h1.set_random()
        self.group_key.h2.set_random()
        self.group_key.h3.set_random()

        ## Compute random generator g2 in G2. Since G2 is a cyclic group of prime
        ## order, just pick a random element
        self.group_key.g2.set_random()

        ## Add the Issuer's public key to the group key
        self.group_key.ipk.set_object(self.group_key.g2 * self.manager_key.isk)

        ## I'll simplify this scheme, instead of using multiple setup calls,
        ## the first call will also generate the converter key
        ## Generate the Converter's private key
        self.manager_key.csk.set_random()

        ## Add the Converter's public key to the group key
        self.group_key.cpk.set_object(self.group_key.g * self.manager_key.csk)

        ## Generate the Extractor's private key
        self.manager_key.esk.set_random()

        ## Add the Extractor's public key to the group key
        self.group_key.epk.set_object(self.group_key.g * self.manager_key.esk)

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## Send a random element to the member
            n = G1.from_random()
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["phase"] = 1  # type: ignore
            ## TODO: This value should be saved in some place to avoid replay attack
        else:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            phase = message["phase"]
            if phase == 2:
                ## Compute credential from H and pi_H. Verify the proof
                n = G1.from_b64(message["n"])
                H = G1.from_b64(message["H"])
                proof = spk.DiscreteLogProof.from_b64(message["pi"])

                if spk.discrete_log_verify(
                    H, self.group_key.h1, proof, n.to_bytes()
                ):
                    ## Pick x and s at random from Z*_p
                    x = Fr.from_random()
                    s = Fr.from_random()

                    life = str(int(time.time() + self.LIFETIME))
                    h = hashlib.sha256(life.encode())
                    ## Modification w.r.t. the GL19 paper: we add a maximum lifetime
                    ## for member credentials. This is done by adding a second message
                    ## to be signed in the BBS+ signatures. This message will then be
                    ## "revealed" (i.e., shared in cleartext) in the SPK computed for
                    ## signing
                    d = Fr.from_hash(h.digest())

                    ## Set A = (H+h_2*s+h3*d+g_1)*((isk+x)**-1)
                    h2s = self.group_key.h2 * s
                    h3d = self.group_key.h3 * d
                    A = (H + h2s + h3d + self.group_key.g1) * ~(
                        self.manager_key.isk + x
                    )

                    ## Mout = (A,x,s,l)
                    ret["status"] = "success"
                    ret["A"] = A.to_b64()
                    ret["x"] = x.to_b64()
                    ret["s"] = s.to_b64()
                    ret["l"] = life
                    ret["phase"] = phase + 1
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("spk.dlog_G1_verify failed")
            else:
                ret["message"] = (
                    f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
                )
                self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, str]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## Parse n and compute (Y,\pi_Y)
            n = G1.from_b64(message["n"])

            ## Compute member's secret key y at random
            member_key.y.set_random()

            ## Compute the member's public key
            member_key.H.set_object(self.group_key.h1 * member_key.y)

            ## Compute the SPK
            proof = spk.discrete_log_sign(
                member_key.H, self.group_key.h1, member_key.y, n.to_bytes()
            )

            ## Build the output message
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["H"] = member_key.H.to_b64()
            ret["pi"] = proof.to_b64()
            ret["phase"] = phase + 1
        elif phase == 3:
            ## Check correctness of computation and update memkey

            # Min = (A,x,s,l)
            member_key.A.set_b64(message["A"])
            member_key.x.set_b64(message["x"])
            member_key.s.set_b64(message["s"])
            member_key.l = int(message["l"])

            ## Recompute h2s from s
            member_key.h2s.set_object(self.group_key.h2 * member_key.s)

            ## Recompute d and h3d from l
            h = hashlib.sha256(str(member_key.l).encode())
            member_key.d.set_hash(h.digest())
            member_key.h3d.set_object(self.group_key.h3 * member_key.d)

            ## Check correctness
            # A must not be 1 (since we use additive notation for G1,
            # it must not be 0)
            if not member_key.A.is_zero():
                e1 = (
                    GT.pairing(member_key.A, self.group_key.g2)
                ) ** member_key.x
                e2 = GT.pairing(member_key.A, self.group_key.ipk)
                e4 = e1 * e2
                aux = (
                    member_key.H
                    + member_key.h2s
                    + member_key.h3d
                    + self.group_key.g1
                )
                e3 = GT.pairing(aux, self.group_key.g2)
                if e4 == e3:
                    ret["status"] = "success"
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("e4 != e3")
            else:
                ret["status"] = "fail"
                ret["message"] = "Invalid message content"
                self._logger.debug("A is zero")
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def sign(self, message: str, member_key: MemberKey) -> dict[str, Any]:
        message = str(message)
        # alpha, r1, r2 \in_R Z_p
        alpha = Fr.from_random()
        r1 = Fr.from_random()
        r2 = Fr.from_random()

        # nym1 = g1*alpha
        sig = Signature()
        sig.nym1.set_object(self.group_key.g * alpha)

        # nym2 = cpk*alpha+h*y
        sig.nym2.set_object(
            (self.group_key.cpk * alpha) + (self.group_key.h * member_key.y)
        )

        ## Add extra encryption of h^y with epk
        alpha2 = Fr.from_random()

        # ehy1 = g*alpha2
        sig.ehy1.set_object(self.group_key.g * alpha2)

        # ehy2 = epk*alpha2+h*y
        sig.ehy2.set_object(
            (self.group_key.epk * alpha2) + (self.group_key.h * member_key.y)
        )

        # AA = A*r1
        sig.AA.set_object(member_key.A * r1)

        ## Good thing we precomputed much of this...
        # aux = (g1+h1*y+h2*s+h3*d)*r1
        aux = (
            self.group_key.g1 + member_key.H + member_key.h2s + member_key.h3d
        ) * r1
        # A_ = AA^{-x}(g1+h*y+h2*s+h3*d)*r1
        sig.A_.set_object(sig.AA * -member_key.x + aux)

        # d = (g1+h1*y+h2*s+h3*d)*r1+h2*-r2
        sig.d.set_object(aux + (self.group_key.h2 * -r2))

        # r3 = r1**-1
        r3 = ~r1

        # ss = s - r2*r3
        ss = member_key.s - (r2 * r3)

        ## Auxiliar variables for the spk
        aux_Zr = -member_key.x
        ss = -ss
        negy = -member_key.y
        A_d = sig.A_ - sig.d

        # g1h3d = g1*h3^d
        g1h3d = self.group_key.g1 + member_key.h3d

        y = [sig.nym1, sig.nym2, A_d, g1h3d, sig.ehy1, sig.ehy2]
        g = [
            self.group_key.g,
            self.group_key.cpk,
            self.group_key.h,
            sig.AA,
            self.group_key.h2,
            sig.d,
            self.group_key.h1,
            self.group_key.epk,
        ]
        x = [aux_Zr, member_key.y, r2, r3, ss, alpha, negy, alpha2]
        i = [
            (5, 0),  # alpha, g
            (5, 1),  # alpha, cpk
            (1, 2),  # y, h
            (0, 3),  # -x, AA
            (2, 4),  # r2, h2
            (3, 5),  # r3, d
            (4, 4),  # ss, h2
            (6, 6),  # -y, h1
            (7, 0),  # alpha2, g
            (7, 7),  # alpha2, epk
            (1, 2),
        ]  # y, h
        prods = [1, 2, 2, 3, 1, 2]

        ## The SPK'ed message becomes the message to sign concatenated with the
        ## credential expiration date
        sig.expiration = member_key.l
        proof = spk.general_representation_sign(
            y, g, x, i, prods, f"{sig.expiration}|{message}"
        )
        sig.pi.c.set_object(proof.c)
        sig.pi.s.extend(proof.s)

        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(self, message: str, signature: str) -> dict[str, Any]:
        message = str(message)
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)

        ## Auxiliar variables for the spk
        A_d = sig.A_ - sig.d

        ## The last sizeof(uint64_t) bytes of the message contain the expiration
        ## date. Parse them, and recompute the h3d value, needed to verify the SPK

        h = hashlib.sha256(str(sig.expiration).encode())
        expiration = Fr.from_hash(h.digest())
        g1h3d = (self.group_key.h3 * expiration) + self.group_key.g1

        y = [sig.nym1, sig.nym2, A_d, g1h3d, sig.ehy1, sig.ehy2]
        g = [
            self.group_key.g,
            self.group_key.cpk,
            self.group_key.h,
            sig.AA,
            self.group_key.h2,
            sig.d,
            self.group_key.h1,
            self.group_key.epk,
        ]
        i = [
            (5, 0),  # alpha, g
            (5, 1),  # alpha, cpk
            (1, 2),  # y, h
            (0, 3),  # -x, AA
            (2, 4),  # r2, h2
            (3, 5),  # r3, d
            (4, 4),  # ss, h2
            (6, 6),  # -y, h1
            (7, 0),  # alpha2, g
            (7, 7),  # alpha2, epk
            (1, 2),
        ]  # y, h
        prods = [1, 2, 2, 3, 1, 2]

        ## Verify SPK
        if spk.general_representation_verify(
            y, g, i, prods, sig.pi, f"{sig.expiration}|{message}"
        ):
            ret["status"] = "success"
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("spk.rep_verify failed")
        return ret

    def blind(
        self, message: str, signature: str, blind_key: BlindKey | None = None
    ) -> dict[str, Any]:
        if blind_key is None:
            blind_key = BlindKey.from_random(self.group_key)
        message = str(message)
        sig = Signature.from_b64(signature)

        ## Pick alpha, beta, gamma at random from Z^*_p
        alpha = Fr.from_random()
        beta = Fr.from_random()
        gamma = Fr.from_random()

        ## Rerandomize the pseudonym encryption under the cpk and
        ## add an encryption layer for the pseudonym under the bpk
        bsig = BlindSignature()
        bsig.nym1.set_object(sig.nym1 + (self.group_key.g * beta))
        bsig.nym2.set_object(self.group_key.g * alpha)
        bsig.nym3.set_object(
            sig.nym2 + (self.group_key.cpk * beta) + (blind_key.pk * alpha)
        )

        ##  Encrypt the (hash of the) message
        h = hashlib.sha256()
        h.update(message.encode())
        c = G1.from_hash(h.digest())
        bsig.c1.set_object(self.group_key.g * gamma)
        bsig.c2.set_object(c + (blind_key.pk * gamma))
        return {
            "status": "success",
            "blind_signature": bsig.to_b64(),
            "blind_key": blind_key.to_b64(),
        }

    def convert(
        self, blind_signatures: list[str], blind_key_public: str
    ) -> dict[str, Any]:
        r = Fr.from_random()
        neg_csk = -self.manager_key.csk
        converted_signatures = []
        pk = G1.from_b64(blind_key_public)
        for bsig_b64 in blind_signatures:
            bsig = BlindSignature.from_b64(bsig_b64)
            r1 = Fr.from_random()
            r2 = Fr.from_random()
            ## Decrypt nym and raise to r
            cnym1p = bsig.nym2 * r
            cnym2p = ((bsig.nym1 * neg_csk) + bsig.nym3) * r

            ## Re-randomize nym
            csig = BlindSignature()
            csig.nym1.set_object(cnym1p + (self.group_key.g * r1))
            csig.nym2.set_object(cnym2p + (pk * r1))
            ## nym3 is empty (default value 0)

            ## Re-randomize ciphertext
            csig.c1.set_object(bsig.c1 + (self.group_key.g * r2))
            csig.c2.set_object(bsig.c2 + (pk * r2))
            converted_signatures.append(csig.to_b64())
        durstenfeld_perm(converted_signatures)
        return {
            "status": "success",
            "converted_signatures": converted_signatures,
        }

    @staticmethod
    def unblind(
        converted_signature: str, blind_key: BlindKey
    ) -> dict[str, Any]:
        csig = BlindSignature.from_b64(converted_signature)
        ## Decrypt the pseudonym with the blinding private key
        aux_zn = -blind_key.sk
        id_ = (csig.nym1 * aux_zn) + csig.nym2

        ## Decrypt the (hashed) message with the blinding private key
        aux_G1 = csig.c1 * aux_zn
        aux_G1 = csig.c2 + aux_G1

        ## Update the received message with the string representation of aux_G1
        ## Really required? It has no use
        return {
            "status": "success",
            "nym": id_.to_b64(),
        }


def durstenfeld_perm(input_list: list[str]) -> None:
    """
    Uses Durstenfeld variant of the Fisher-Yates in place permutation
    algorithm to output a random permutation of the given array.

    See https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle#The_modern_algorithm
    for a definition of the algorithm.
    """
    for i in range(len(input_list) - 2):
        j = random.randint(i, len(input_list))
        tmp = input_list[i]
        input_list[i] = input_list[j]
        input_list[j] = tmp

```

### pygroupsig\schemes\klap20.py

```
import hashlib
import logging
import random
from typing import Any

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    GML,
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "klap20"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    g: G1
    gg: G2
    XX: G2
    YY: G2
    ZZ0: G2
    ZZ1: G2

    def __init__(self) -> None:
        self.g = G1()  # Random generator of G1
        self.gg = G2()  # Random generator of G1
        self.XX = G2()  # gg^x (x is part of mgrkey)
        self.YY = G2()  # gg^y (y is part of mgrkey)
        self.ZZ0 = G2()  # gg^z0 (z0 is part of mgrkey)
        self.ZZ1 = G2()  # gg^z1 (z1 is part of mgrkey)


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    x: Fr
    y: Fr
    z0: Fr
    z1: Fr

    def __init__(self) -> None:
        self.x = Fr()  # Issuer component x
        self.y = Fr()  # Issuer component y
        self.z0 = Fr()  # Opener component z_0
        self.z1 = Fr()  # Opener component z_1


class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    alpha: Fr
    u: G1
    v: G1
    w: G1

    def __init__(self) -> None:
        self.alpha = Fr()
        self.u = G1()
        self.v = G1()
        self.w = G1()


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    uu: G1
    vv: G1
    ww: G1
    pi: spk.DiscreteLogProof

    def __init__(self) -> None:
        self.uu = G1()
        self.vv = G1()
        self.ww = G1()
        self.pi = spk.DiscreteLogProof()


class Group(
    JoinMixin, ReprMixin, MetadataMixin, Scheme[GroupKey, ManagerKey, MemberKey]
):
    _logger = logging.getLogger(__name__)

    gml: GML

    def __init__(self) -> None:
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()
        self.gml = GML()

    def setup(self) -> None:
        ## Initializes the Issuer's key
        self.manager_key.x.set_random()
        self.manager_key.y.set_random()

        ## Initializes the Group key
        # Compute random generators g and gg. Since G1 and G2 are cyclic
        # groups of prime order, just pick random elements
        self.group_key.g.set_random()
        self.group_key.gg.set_random()

        ## Partially fill the group key with the Issuer's public key
        self.group_key.XX.set_object(self.group_key.gg * self.manager_key.x)
        self.group_key.YY.set_object(self.group_key.gg * self.manager_key.y)

        ## Initialize the Opener's key
        self.manager_key.z0.set_random()
        self.manager_key.z1.set_random()

        ## Finalize the group key with the Opener's public key
        self.group_key.ZZ0.set_object(self.group_key.gg * self.manager_key.z0)
        self.group_key.ZZ1.set_object(self.group_key.gg * self.manager_key.z1)

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## Send a random element to the member
            n = G1.from_random()
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["phase"] = 1  # type: ignore
            ## TODO: This value should be saved in some place to avoid replay attack
        else:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            phase = message["phase"]
            if phase == 2:
                ## Import the (n,f,w,SSO,SS1,ff0,ff1,pi) ad hoc message
                n = G1.from_b64(message["n"])
                f = G1.from_b64(message["f"])
                w = G1.from_b64(message["w"])
                SS0 = G2.from_b64(message["SS0"])
                SS1 = G2.from_b64(message["SS1"])
                ff0 = G2.from_b64(message["ff0"])
                ff1 = G2.from_b64(message["ff1"])
                proof = spk.GeneralRepresentationProof.from_b64(message["pi"])
                ## Check the SPK -- this will change with issue23
                ## Compute the SPK for sk -- this will be replaced in issue23
                # u = Hash(f)
                h = hashlib.sha256(f.to_bytes())
                u = G1.from_hash(h.digest())

                y = [f, w, SS0, SS1, ff0, ff1]
                g = [
                    self.group_key.g,
                    u,
                    self.group_key.gg,
                    self.group_key.ZZ0,
                    self.group_key.ZZ1,
                ]
                i = [
                    (0, 0),  # alpha, g
                    (0, 1),  # alpha, u
                    (1, 2),  # s0, gg
                    (2, 2),  # s1, gg
                    (0, 2),  # alpha, gg
                    (1, 3),  # s0, ZZ0
                    (0, 2),  # alpha, gg
                    (2, 4),
                ]  # s1, ZZ1
                prods = [1, 1, 1, 1, 2, 2]
                if spk.general_representation_verify(
                    y, g, i, prods, proof, n.to_bytes(), manual=True
                ):
                    v = (u * self.manager_key.x) + (w * self.manager_key.y)
                    # Add the tuple (i,SS0,SS1,ff0,ff1,tau) to the GML
                    tau = GT.pairing(f, self.group_key.gg)
                    # Currently, KLAP20 identities are just uint64_t's
                    h = hashlib.sha256()
                    h.update(SS0.to_bytes())
                    h.update(SS1.to_bytes())
                    h.update(ff0.to_bytes())
                    h.update(ff1.to_bytes())
                    h.update(tau.to_bytes())

                    self.gml[h.hexdigest()] = (SS0, SS1, ff0, ff1, tau)

                    ret["status"] = "success"
                    ret["v"] = v.to_b64()
                    ret["phase"] = phase + 1
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("spk.verify failed")
            else:
                ret["message"] = (
                    f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
                )
                self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, Any]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## The manager sends a random element in G1
            n = G1.from_b64(message["n"])

            ## Compute secret alpha, s0 and s1
            member_key.alpha.set_random()

            s0 = Fr.from_random()
            s1 = Fr.from_random()

            # f = g*alpha
            f = self.group_key.g * member_key.alpha
            # u = Hash(f)
            h = hashlib.sha256(f.to_bytes())
            member_key.u.set_hash(h.digest())

            # w = u*alpha
            member_key.w.set_object(member_key.u * member_key.alpha)

            # SS0 = gg*s0
            SS0 = self.group_key.gg * s0
            # SS1 = gg*s1
            SS1 = self.group_key.gg * s1

            ggalpha = self.group_key.gg * member_key.alpha
            # ff0 = gg*alpha+ZZ0*s0
            ff0 = ggalpha + (self.group_key.ZZ0 * s0)
            # ff1 = gg*alpha+ZZ1*s1
            ff1 = ggalpha + (self.group_key.ZZ1 * s1)

            # tau = e(f,gg)
            # tau = GT.pairing(f, self.grpkey.gg)
            ## TODO: Whats the usage of tau in this current phase?

            y = [f, member_key.w, SS0, SS1, ff0, ff1]
            g = [
                self.group_key.g,
                member_key.u,
                self.group_key.gg,
                self.group_key.ZZ0,
                self.group_key.ZZ1,
            ]
            x = [member_key.alpha, s0, s1]
            i = [
                (0, 0),  # alpha, g
                (0, 1),  # alpha, u
                (1, 2),  # s0, gg
                (2, 2),  # s1, gg
                (0, 2),  # alpha, gg
                (1, 3),  # s0, ZZ0
                (0, 2),  # alpha, gg
                (2, 4),  # s1, ZZ1
            ]
            prods = [1, 1, 1, 1, 2, 2]
            proof = spk.general_representation_sign(
                y, g, x, i, prods, n.to_bytes(), manual=True
            )
            ## Need to send (n, f, w, SS0, SS1, ff0, ff1, pi): prepare ad hoc message
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["f"] = f.to_b64()
            ret["w"] = member_key.w.to_b64()
            ret["SS0"] = SS0.to_b64()
            ret["SS1"] = SS1.to_b64()
            ret["ff0"] = ff0.to_b64()
            ret["ff1"] = ff1.to_b64()
            ret["pi"] = proof.to_b64()
            ret["phase"] = phase + 1
        elif phase == 3:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            # Min = v
            member_key.v = G1.from_b64(message["v"])
            # Check correctness: e(v,gg) = e(u,XX)e(w,YY)
            e1 = GT.pairing(member_key.v, self.group_key.gg)
            e2 = GT.pairing(member_key.u, self.group_key.XX)
            e3 = GT.pairing(member_key.w, self.group_key.YY)
            e4 = e2 * e3
            if e1 == e4:
                ret["status"] = "success"
            else:
                ret["status"] = "fail"
                ret["message"] = "Invalid message content"
                self._logger.debug("e1 != e4")
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def sign(self, message: str, member_key: MemberKey) -> dict[str, Any]:
        message = str(message)
        ## Randomize u, v and w
        r = Fr.from_random()
        sig = Signature()
        sig.uu.set_object(member_key.u * r)
        sig.vv.set_object(member_key.v * r)
        sig.ww.set_object(member_key.w * r)

        ## Compute signature of knowledge of alpha
        proof = spk.discrete_log_sign(sig.ww, sig.uu, member_key.alpha, message)
        sig.pi.c.set_object(proof.c)
        sig.pi.s.set_object(proof.s)
        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(self, message: str, signature: str) -> dict[str, Any]:
        message = str(message)
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        ## Verify SPK
        if spk.discrete_log_verify(sig.ww, sig.uu, sig.pi, message):
            # e1 = e(vv,gg)
            e1 = GT.pairing(sig.vv, self.group_key.gg)
            # e2 = e(uu,XX)
            e2 = GT.pairing(sig.uu, self.group_key.XX)
            # e3 = e(ww,YY)
            e3 = GT.pairing(sig.ww, self.group_key.YY)
            e4 = e2 * e3
            ## Compare the result with the received challenge
            if e1 == e4:
                ret["status"] = "success"
            else:
                ret["message"] = "Invalid signature"
                self._logger.debug("e1 != e4")
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("spk.dlog_G1_verify failed")
        return ret

    def open(self, signature: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        b = random.randint(0, 1)
        for mem_id, (SS0, SS1, ff0, ff1, tau) in self.gml.items():
            if b:
                aux = -(SS1 * self.manager_key.z1)
                ff = ff1 + aux
            else:
                aux = -(SS0 * self.manager_key.z0)
                ff = ff0 + aux
            e1 = GT.pairing(sig.uu, ff)
            e2 = GT.pairing(sig.ww, self.group_key.gg)
            e3 = GT.pairing(self.group_key.g, ff)
            if e1 == e2 and tau == e3:
                ret["status"] = "success"
                ret["id"] = mem_id
                proof = spk.pairing_homomorphism_sign2(
                    ff, sig.uu, self.group_key.g, e2, e3, tau, sig.to_b64()
                )
                ret["proof"] = proof.to_b64()
                break
        return ret

    def open_verify(self, signature: str, proof: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        proof_ = spk.PairingHomomorphismProof2.from_b64(proof)
        e2 = GT.pairing(sig.ww, self.group_key.gg)
        if spk.pairing_homomorphism_verify2(
            proof_, sig.uu, self.group_key.g, e2, sig.to_b64()
        ):
            ret["status"] = "success"
        return ret

```

### pygroupsig\schemes\ps16.py

```
import hashlib
import logging
from typing import Any

import pygroupsig.utils.spk as spk
from pygroupsig.interfaces import Container, Scheme
from pygroupsig.utils.helpers import (
    GML,
    B64Mixin,
    InfoMixin,
    JoinMixin,
    MetadataGroupKeyMixin,
    MetadataManagerKeyMixin,
    MetadataMemberKeyMixin,
    MetadataSignatureMixin,
    ReprMixin,
)
from pygroupsig.utils.mcl import G1, G2, GT, Fr


class MetadataMixin:
    _name = "ps16"


class GroupKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataGroupKeyMixin,
    MetadataMixin,
    Container,
):
    g: G1
    gg: G2
    X: G2
    Y: G2

    def __init__(self) -> None:
        self.g = G1()  # Random generator of G1
        self.gg = G2()  # Random generator of G2
        self.X = G2()  # gg^x (x is part of mgrkey)
        self.Y = G2()  # gg^y (y is part of mgrkey)


class ManagerKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataManagerKeyMixin,
    MetadataMixin,
    Container,
):
    x: Fr
    y: Fr

    def __init__(self) -> None:
        self.x = Fr()
        self.y = Fr()


class MemberKey(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataMemberKeyMixin,
    MetadataMixin,
    Container,
):
    sk: Fr
    sigma1: G1
    sigma2: G1

    def __init__(self) -> None:
        self.sk = Fr()
        self.sigma1 = G1()
        self.sigma2 = G1()


class Signature(
    B64Mixin,
    InfoMixin,
    ReprMixin,
    MetadataSignatureMixin,
    MetadataMixin,
    Container,
):
    sigma1: G1
    sigma2: G1
    pi: spk.DiscreteLogProof

    def __init__(self) -> None:
        self.sigma1 = G1()
        self.sigma2 = G1()
        self.pi = spk.DiscreteLogProof()


class Group(
    JoinMixin, ReprMixin, MetadataMixin, Scheme[GroupKey, ManagerKey, MemberKey]
):
    _logger = logging.getLogger(__name__)

    gml: GML

    def __init__(self) -> None:
        self.group_key = GroupKey()
        self.manager_key = ManagerKey()
        self.gml = GML()

    def setup(self) -> None:
        ## Set manager key
        self.manager_key.x.set_random()
        self.manager_key.y.set_random()

        ## Set group key
        self.group_key.g.set_random()
        self.group_key.gg.set_random()
        self.group_key.X.set_object(self.group_key.gg * self.manager_key.x)
        self.group_key.Y.set_object(self.group_key.gg * self.manager_key.y)

    def join_mgr(self, message: dict[str, Any] | None = None) -> dict[str, Any]:
        ret = {"status": "error"}
        if message is None:
            ## Send a random element to the member, the member should send it back
            ## to us. This way replay attacks are mitigated
            n = G1.from_random()
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["phase"] = 1  # type: ignore
        else:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            phase = message["phase"]
            if phase == 2:
                ## Import the (n,tau,ttau,pi) ad hoc message
                n = G1.from_b64(message["n"])
                tau = G1.from_b64(message["tau"])
                ttau = G2.from_b64(message["ttau"])
                proof = spk.DiscreteLogProof.from_b64(message["pi"])

                if spk.discrete_log_verify(
                    tau, self.group_key.g, proof, n.to_bytes()
                ):
                    e1 = GT.pairing(tau, self.group_key.Y)
                    e2 = GT.pairing(self.group_key.g, ttau)

                    if e1 == e2:
                        ## Compute the partial member key
                        u = Fr.from_random()
                        sigma1 = self.group_key.g * u
                        sigma2 = (
                            (tau * self.manager_key.y)
                            + (self.group_key.g * self.manager_key.x)
                        ) * u

                        ## Add the tuple (i,tau,ttau) to the GML
                        h = hashlib.sha256()
                        h.update(tau.to_bytes())
                        h.update(ttau.to_bytes())
                        self.gml[h.hexdigest()] = (tau, ttau)

                        ## Mout = (sigma1,sigma2)
                        ret["status"] = "success"
                        ret["sigma1"] = sigma1.to_b64()
                        ret["sigma2"] = sigma2.to_b64()
                        ret["phase"] = phase + 1
                    else:
                        ret["status"] = "fail"
                        ret["message"] = "Invalid message content"
                        self._logger.debug("e1 != e2")
                else:
                    ret["status"] = "fail"
                    ret["message"] = "Invalid message content"
                    self._logger.debug("spk.dlog_G1_verify failed")
            else:
                ret["message"] = (
                    f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
                )
                self._logger.error(ret["message"])
        return ret

    def join_mem(
        self, message: dict[str, Any], member_key: MemberKey
    ) -> dict[str, Any]:
        ret = {"status": "error"}
        if not isinstance(message, dict):
            ret["message"] = "Invalid message type. Expected dict"
            self._logger.error(ret["message"])
            return ret
        phase = message["phase"]
        if phase == 1:
            ## The manager sends a random element in G1
            n = G1.from_b64(message["n"])

            ## Compute secret exponent, tau and ttau
            member_key.sk.set_random()
            tau = self.group_key.g * member_key.sk
            ttau = self.group_key.Y * member_key.sk

            ## Compute the SPK for sk
            proof = spk.discrete_log_sign(
                tau, self.group_key.g, member_key.sk, n.to_bytes()
            )

            ## Build the output message
            ret["status"] = "success"
            ret["n"] = n.to_b64()
            ret["tau"] = tau.to_b64()
            ret["ttau"] = ttau.to_b64()
            ret["pi"] = proof.to_b64()
            ret["phase"] = phase + 1
        elif phase == 3:
            if not isinstance(message, dict):
                ret["message"] = "Invalid message type. Expected dict"
                self._logger.error(ret["message"])
                return ret
            ## Check correctness of computation and update memkey

            # We have sk in memkey, so just need to copy the
            # sigma1 and sigma2 values from the received message,
            # which is an exported (partial) memkey
            member_key.sigma1.set_b64(message["sigma1"])
            member_key.sigma2.set_b64(message["sigma2"])
            ret["status"] = "success"
        else:
            ret["message"] = (
                f"Phase not supported for {self.__class__.__name__}{self._name.upper()}"
            )
            self._logger.error(ret["message"])
        return ret

    def sign(self, message: str, member_key: MemberKey) -> dict[str, Any]:
        message = str(message)

        ## Randomize sigma1 and sigma2
        t = Fr.from_random()
        sig = Signature()
        sig.sigma1.set_object(member_key.sigma1 * t)
        sig.sigma2.set_object(member_key.sigma2 * t)

        ## Compute signature of knowledge of sk
        # The SPK in PS16 is a dlog spk, but does not follow exactly the
        # pattern of spk_dlog, so we must implement it manually.
        # A good improvement would be to analyze how to generalize spk_dlog
        # to fit this
        k = Fr.from_random()
        e = (GT.pairing(sig.sigma1, self.group_key.Y)) ** k

        # c = hash(ps16_sig->sigma1,ps16_sig->sigma2,e,m)
        h = hashlib.sha256()
        h.update(sig.sigma1.to_bytes())
        h.update(sig.sigma2.to_bytes())
        h.update(e.to_bytes())
        h.update(message.encode())

        ## Complete the sig
        sig.pi.c.set_hash(h.digest())
        sig.pi.s.set_object(k + (sig.pi.c * member_key.sk))
        return {
            "status": "success",
            "signature": sig.to_b64(),
        }

    def verify(self, message: str, signature: str) -> dict[str, Any]:
        message = str(message)
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)

        # e1 = e(-sigma1,X)
        e1 = GT.pairing(-sig.sigma1, self.group_key.X)
        # e2 = e(sigma2,gg)
        e2 = GT.pairing(sig.sigma2, self.group_key.gg)
        # e3 = e(sigma1*s,Y)
        e3 = GT.pairing(sig.sigma1 * sig.pi.s, self.group_key.Y)

        # R = ((e1*e2)**-c)*e3
        R = ~((e1 * e2) ** sig.pi.c) * e3

        h = hashlib.sha256()
        h.update(sig.sigma1.to_bytes())
        h.update(sig.sigma2.to_bytes())
        h.update(R.to_bytes())
        h.update(message.encode())

        ## Complete the sig
        c = Fr.from_hash(h.digest())

        ## Compare the result with the received challenge
        if c == sig.pi.c:
            ret["status"] = "success"
        else:
            ret["message"] = "Invalid signature"
            self._logger.debug("c != sig.pi.c")
        return ret

    def open(self, signature: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        e1 = GT.pairing(sig.sigma2, self.group_key.gg)
        e2 = GT.pairing(sig.sigma1, self.group_key.X)
        e4 = e1 / e2
        for mem_id, (_, ttau) in self.gml.items():
            e3 = GT.pairing(sig.sigma1, ttau)
            if e4 == e3:
                ret["status"] = "success"
                ret["id"] = mem_id
                proof = spk.pairing_homomorphism_sign(
                    sig.sigma1, e3, ttau, sig.to_b64()
                )
                ret["proof"] = proof.to_b64()
                break
        return ret

    def open_verify(self, signature: str, proof: str) -> dict[str, Any]:
        ret = {"status": "fail"}
        sig = Signature.from_b64(signature)
        proof_ = spk.PairingHomomorphismProof.from_b64(proof)
        e1 = GT.pairing(sig.sigma2, self.group_key.gg)
        e2 = GT.pairing(sig.sigma1, self.group_key.X)
        e4 = e1 / e2
        if spk.pairing_homomorphism_verify(
            sig.sigma1, e4, proof_, sig.to_b64()
        ):
            ret["status"] = "success"
        return ret

```

### pygroupsig\utils\constants.py

```
import ctypes
import os
import sys

# src/bn_c384_256.cpp
MCLBN_FP_UNIT_SIZE: int = 6
MCLBN_FR_UNIT_SIZE: int = 4

# include/lib/curve_type.h
MCL_BLS12_381: int = 5

# include/lib/bn.h
MCLBN_COMPILED_TIME_VAR: int = MCLBN_FR_UNIT_SIZE * 10 + MCLBN_FP_UNIT_SIZE

# src/shim/pbc_ext.h (libgroupsig)
BLS12_381_P: str = "1 3685416753713387016781088315183077757961620795782546409894578378688607592378376318836054947676345821548104185464507 1339506544944476473020471379941921221584933875938349620426543736416511423956333506472724655353366534992391756441569"
BLS12_381_Q: str = "1 352701069587466618187139116011060144890029952792775240219908644239793785735715026873347600343865175952761926303160 3059144344244213709971259814753781636986470325476647558659373206291635324768958432433509563104347017837885763365758 1985150602287291935568054521177171638300868978215655730859378665066344726373823718423869104263333984641494340347905 927553665492332455747201965776037880757740193453592970025027978793976877002675564980949289727957565575433344219582"

# Get the absolute path to the mcl/build/lib directory
# For Windows paths in WSL, we need to convert from /mnt/c/... to C:\...
if os.path.exists('/mnt/c'):
    # We're in WSL
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    mcl_build_lib = os.path.join(project_root, "mcl", "build", "lib")
    print(f"Project root: {project_root}")
    print(f"MCL build lib path: {mcl_build_lib}")
else:
    # We're in a regular Linux environment
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    mcl_build_lib = os.path.join(project_root, "mcl", "build", "lib")

# In pygroupsig/utils/constants.py
LIB_PATH: str = os.environ.get("MCL_LIB_PATH", mcl_build_lib)
MCL_LIB: str = "libmcl.so"
MCL384_LIB: str = "libmclbn384_256.so"
lib: ctypes.CDLL | None = None


def load_library() -> None:
    global lib
    if not LIB_PATH:
        raise RuntimeError("Environment variable MCL_LIB_PATH missing.")

    print(f"Loading MCL libraries from: {LIB_PATH}")
    try:
        # Check if the library files exist
        mcl_lib_path = os.path.join(LIB_PATH, MCL_LIB)
        mcl384_lib_path = os.path.join(LIB_PATH, MCL384_LIB)

        if not os.path.exists(mcl_lib_path):
            print(f"Warning: {mcl_lib_path} does not exist")
            # Try to find the library in the system
            for path in sys.path:
                potential_path = os.path.join(path, MCL_LIB)
                if os.path.exists(potential_path):
                    print(f"Found {MCL_LIB} at {potential_path}")
                    mcl_lib_path = potential_path
                    break

        if not os.path.exists(mcl384_lib_path):
            print(f"Warning: {mcl384_lib_path} does not exist")
            # Try to find the library in the system
            for path in sys.path:
                potential_path = os.path.join(path, MCL384_LIB)
                if os.path.exists(potential_path):
                    print(f"Found {MCL384_LIB} at {potential_path}")
                    mcl384_lib_path = potential_path
                    break

        ctypes.CDLL(mcl_lib_path)
        lib = ctypes.CDLL(mcl384_lib_path)
        if lib.mclBn_init(MCL_BLS12_381, MCLBN_COMPILED_TIME_VAR):
            raise RuntimeError("mcl library could not be initialized")
    except Exception as e:
        print(f"Error loading MCL libraries: {e}")
        raise

```

### pygroupsig\utils\helpers.py

```
# mypy: disable-error-code="misc"

import importlib
import json
from base64 import b64decode, b64encode
from typing import Any, KeysView, Type, TypeVar

from pygroupsig.interfaces import Container
from pygroupsig.utils.mcl import Fr

_SEQ = 3
_START = 0

T = TypeVar("T", bound="Container")


class ReprMixin:
    def __repr__(self) -> str:
        rep = json.dumps({k: str(v) for k, v in vars(self).items()})
        return f"{self.__class__} {rep}"


class InfoMixin:
    _name: str
    _container_name: str

    def info(self) -> tuple[str, str, KeysView]:
        return self._name, self._container_name, vars(self).keys()


# noinspection PyUnresolvedReferences
class B64Mixin:
    def to_b64(self) -> str:
        scheme_name, container_name, var = self.info()  # type: ignore
        dump: dict[str, Any] = {}
        for v in var:
            obj = getattr(self, v)
            if isinstance(obj, list):
                dump[v] = [el.to_b64() for el in obj]
            elif isinstance(obj, int) or isinstance(obj, dict):
                dump[v] = obj
            elif isinstance(obj, str):
                dump[v] = f"str_{obj}"
            else:
                dump[v] = obj.to_b64()
        data = b64encode(json.dumps(dump).encode()).decode()
        if container_name == "signature":
            msg = {"scheme": scheme_name, "signature": data}
        else:
            msg = {"scheme": scheme_name, "type": container_name, "key": data}
        return b64encode(json.dumps(msg).encode()).decode()

    def set_b64(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = s.encode()
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        data = json.loads(b64decode(s))
        if "key" in data or "signature" in data:
            if "key" in data:
                d = data["key"]
            else:
                d = data["signature"]
            it = json.loads(b64decode(d.encode()))
        else:
            it = data
        for k, v in it.items():
            obj = getattr(self, k)
            if isinstance(it[k], list):
                obj.extend([Fr.from_b64(el) for el in it[k]])
            elif isinstance(it[k], int) or isinstance(it[k], dict):
                setattr(self, k, it[k])
            elif it[k].startswith("str_"):
                setattr(self, k, it[k].split("_")[1])
            else:
                obj.set_b64(it[k])

    @classmethod
    def from_b64(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_b64(s)
        return ret


class JoinMixin:
    @staticmethod
    def join_seq() -> int:
        return _SEQ

    @staticmethod
    def join_start() -> int:
        return _START


class ContainerDict(dict):
    def to_b64(self) -> str:
        exp = {}
        for el, values in self.items():
            exp[el] = [
                f"{v.__class__.__module__}.{v.__class__.__name__}|{v.to_b64()}"
                for v in values
            ]
        return b64encode(json.dumps(exp).encode()).decode()

    def set_b64(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = s.encode()
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        imp = json.loads(b64decode(s))
        for mem_id, data in imp.items():
            values = []
            for el in data:
                c, v = el.split("|")
                path = c.split(".")
                module_name, class_name = ".".join(path[:-1]), path[-1]
                mod = importlib.import_module(module_name)
                cls = getattr(mod, class_name)
                values.append(cls.from_b64(v))
            self[mem_id] = tuple(values)

    @classmethod
    def from_b64(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_b64(s)
        return ret


GML = ContainerDict
CRL = ContainerDict


class MetadataGroupKeyMixin:
    _container_name = "group"


class MetadataManagerKeyMixin:
    _container_name = "manager"


class MetadataMemberKeyMixin:
    _container_name = "member"


class MetadataBlindKeyMixin:
    _container_name = "blind"


class MetadataSignatureMixin:
    _container_name = "signature"

```

### pygroupsig\utils\mcl.py

```
# mypy: disable-error-code="misc,attr-defined,type-var,operator"

import ctypes
import logging
from base64 import b64decode, b64encode
from typing import Any, Type, TypeVar

from typing_extensions import Self

import pygroupsig.utils.constants as ct

T = TypeVar("T", bound="Base")


class Base(ctypes.Structure):
    # ffi/go/lib/lib.go
    BUFFER_SZ: int = 2048
    MCL: str = "mclBn{}_{}"

    def __str__(self) -> str:
        return f"{self.__class__} {self.get_str()}"

    def __repr__(self) -> str:
        return str(self)

    def is_zero(self) -> bool:
        return bool(self._call("isZero"))

    def __eq__(self, y: Self) -> bool:  # type: ignore
        return bool(self._call("isEqual", y))

    def is_equal(self, y: Self) -> bool:
        return self.__eq__(y)

    def __neg__(self) -> Self:
        return self._call("neg", ret=True)

    def neg(self) -> Self:
        return self.__neg__()

    def __add__(self, y: Self) -> Self:
        return self._call("add", y, ret=True)

    def add(self, y: Self) -> Self:
        return self.__add__(y)

    def __sub__(self, y: Self) -> Self:
        return self._call("sub", y, ret=True)

    def sub(self, y: Self) -> Self:
        return self.__sub__(y)

    def __mul__(self, y: Self) -> Self:
        return self._call("mul", y, ret=True)

    def mul(self, y: Self) -> Self:
        return self.__mul__(y)

    def to_bytes(self) -> bytes:
        func = self._func(
            "serialize",
            [
                ctypes.c_char_p,
                ctypes.c_size_t,
                ctypes.POINTER(self.__class__),
            ],
            ctypes.c_size_t,
        )
        buffer = ctypes.create_string_buffer(self.BUFFER_SZ)
        sz = func(buffer, self.BUFFER_SZ, self)
        return buffer.raw[:sz]

    def set_bytes(self, buffer: bytes) -> None:
        func = self._func(
            "deserialize",
            [
                ctypes.POINTER(self.__class__),
                ctypes.c_char_p,
                ctypes.c_size_t,
            ],
            ctypes.c_size_t,
        )
        func(self, buffer, len(buffer))

    @classmethod
    def from_bytes(cls: Type[T], buffer: bytes) -> T:
        ret = cls()
        ret.set_bytes(buffer)
        return ret

    def to_hex(self) -> str:
        return "".join([f"{b:02x}" for b in self.to_bytes()])

    def to_b64(self) -> str:
        return b64encode(self.to_bytes()).decode()

    def set_b64(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = s.encode()
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        return self.set_bytes(b64decode(s))

    @classmethod
    def from_b64(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_b64(s)
        return ret

    def set_object(self, y: Self) -> None:
        return self.set_bytes(y.to_bytes())

    @classmethod
    def from_object(cls: Type[T], y: T) -> T:
        return cls.from_bytes(y.to_bytes())

    @classmethod
    def byte_size(cls: Type[T]) -> int:
        func = getattr(ct.lib, f"mclBn_get{cls.__name__}ByteSize")
        func.restype = ctypes.c_int
        return func()

    def to_file(self, file: str) -> None:
        with open(file, "w") as f:
            f.write(self.to_b64())

    def set_file(self, file: str) -> None:
        with open(file) as f:
            return self.set_b64(f.read())

    @classmethod
    def from_file(cls: Type[T], file: str) -> T:
        ret = cls()
        ret.set_file(file)
        return ret

    def _call(self, fn: str, y: Self | None = None, ret: bool = False) -> Any:
        argtypes = [ctypes.POINTER(self.__class__)] * (1 if y is None else 2)
        restype = None if ret else ctypes.c_int
        if ret:
            argtypes.append(ctypes.POINTER(self.__class__))
        func = self._func(fn, argtypes, restype)
        if ret:
            obj = self.__class__()
            if y is None:
                func(obj, self)
            else:
                func(obj, self, y)
            return obj
        else:
            if y is None:
                return func(self)
            return func(self, y)

    def _func(
        self, fn: str, argtypes: list[Any], restype: Any | None = None
    ) -> Any:
        func = getattr(ct.lib, self.MCL.format(self.__class__.__name__, fn))
        func.argtypes = argtypes
        func.restype = restype
        return func


# noinspection PyUnresolvedReferences
class StrMixin:
    def set_str(self, s: str | bytes, mode: int = 10) -> None:
        if isinstance(s, str):
            s = s.encode()
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        func = self._func(
            "setStr",
            [
                ctypes.POINTER(self.__class__),
                ctypes.c_char_p,
                ctypes.c_size_t,
                ctypes.c_int,
            ],
            ctypes.c_int,
        )
        if func(self, s, len(s), mode):
            raise RuntimeError(
                f"Failed to call {self.__class__.__name__}.setStr()"
            )

    @classmethod
    def from_str(cls: Type[T], s: str | bytes, mode: int = 10) -> T:
        ret = cls()
        ret.set_str(s, mode)
        return ret

    def get_str(self, mode: int = 10) -> str:
        buf = ctypes.create_string_buffer(self.BUFFER_SZ)
        func = self._func(
            "getStr",
            [
                ctypes.c_char_p,
                ctypes.c_size_t,
                ctypes.POINTER(self.__class__),
                ctypes.c_int,
            ],
            ctypes.c_int,
        )
        if not func(buf, self.BUFFER_SZ, self, mode):
            raise RuntimeError(
                f"Failed to call {self.__class__.__name__}.getStr()"
            )
        return buf.value.decode()


# noinspection PyUnresolvedReferences
class HashRandomCmpMixin:
    def set_hash(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = bytes(bytearray.fromhex(s))
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        func = self._func(
            "setHashOf",
            [
                ctypes.POINTER(self.__class__),
                ctypes.c_char_p,
                ctypes.c_size_t,
            ],
            ctypes.c_int,
        )
        if func(self, s, len(s)):
            raise RuntimeError(
                f"Failed to call {self.__class__.__name__}.setHashOf()"
            )

    @classmethod
    def from_hash(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_hash(s)
        return ret

    def set_random(self) -> None:
        func = self._func(
            "setByCSPRNG",
            [ctypes.POINTER(self.__class__)],
            ctypes.c_int,
        )
        if func(self):
            raise RuntimeError(
                f"Failed to {self.__class__.__name__}.setByCSPNRG()"
            )

    @classmethod
    def from_random(cls: Type[T]) -> T:
        ret = cls()
        ret.set_random()
        return ret

    def cmp(self, y: Self) -> int:
        return self._call("cmp", y)

    def __gt__(self, y: Self) -> bool:
        return self._call("cmp", y) == 1

    def __ge__(self, y: Self) -> bool:
        return self.__gt__(y) or self.__eq__(y)

    def __lt__(self, y: Self) -> bool:
        return self._call("cmp", y) == -1

    def __le__(self, y: Self) -> bool:
        return self.__lt__(y) or self.__eq__(y)


# noinspection PyUnresolvedReferences
class OneInvDivMixin:
    def is_one(self) -> bool:
        return bool(self._call("isOne"))

    def __invert__(self) -> Self:
        return self._call("inv", ret=True)

    def inv(self) -> Self:
        return self.__invert__()

    def __truediv__(self, y: Self) -> Self:
        return self._call("div", y, ret=True)

    def div(self, y: Self) -> Self:
        return self.__truediv__(y)


# noinspection PyUnresolvedReferences
class MulFrMixin:
    def __mul__(self, y: "Fr") -> Self:
        func = self._func(
            "mul", [ctypes.POINTER(self.__class__)] * 2 + [ctypes.POINTER(Fr)]
        )
        ret = self.__class__()
        func(ret, self, y)
        return ret

    def mul(self, y: "Fr") -> Self:
        return self.__mul__(y)


class MulVecMixin:
    @classmethod
    def muln(cls: Type[T], x: ctypes.Array[T], y: ctypes.Array["Fr"]) -> T:
        if len(x) != len(y):
            logging.warn(f"muln: len(x)={len(x)} != len(y)={len(y)}")
        ret = cls()
        func = ret._func(  # noqa
            "mulVec",
            [ctypes.POINTER(cls)] * 2 + [ctypes.POINTER(Fr), ctypes.c_size_t],
        )
        func(ret, x, y, min(len(x), len(y)))
        return ret


# noinspection PyUnresolvedReferences
class PowMixin:
    def __pow__(self, y: Self) -> Self:
        return self._call("pow", y, ret=True)

    def pow(self, y: Self) -> Self:
        return self.__pow__(y)


# noinspection PyUnresolvedReferences
class PowFrMixin:
    def __pow__(self, y: "Fr") -> Self:
        func = self._func(
            "pow", [ctypes.POINTER(self.__class__)] * 2 + [ctypes.POINTER(Fr)]
        )
        ret = self.__class__()
        func(ret, self, y)
        return ret

    def pow(self, y: "Fr") -> Self:
        return self.__pow__(y)


# noinspection PyUnresolvedReferences
class HashAndMapMixin:
    def set_hash(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = bytes(bytearray.fromhex(s))
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        func = self._func(
            "hashAndMapTo",
            [
                ctypes.POINTER(self.__class__),
                ctypes.c_char_p,
                ctypes.c_size_t,
            ],
            ctypes.c_int,
        )
        if func(self, s, len(s)):
            raise RuntimeError(
                f"Failed to call {self.__class__.__name__}.hashAndMapTo()"
            )

    @classmethod
    def from_hash(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_hash(s)
        return ret


# noinspection PyUnresolvedReferences
class IntMixin:
    def set_int(self, i: int) -> None:
        if not isinstance(i, int):
            raise TypeError(f"Invalid {i} type. Expected int")
        func = self._func(
            "setInt", [ctypes.POINTER(self.__class__), ctypes.c_int64]
        )
        func(self, i)

    @classmethod
    def from_int(cls: Type[T], i: int) -> T:
        ret = cls()
        ret.set_int(i)
        return ret


# noinspection PyUnresolvedReferences
class GeneratorMixin:
    def set_generator(self) -> None:
        s = ct.BLS12_381_P
        if isinstance(self, G2):
            s = ct.BLS12_381_Q
        self.set_str(s)

    @classmethod
    def from_generator(cls: Type[T]) -> T:
        ret = cls()
        ret.set_generator()
        return ret

    def set_random(self) -> None:
        gen = self.__class__()
        gen.set_generator()
        r = Fr()
        r.set_random()
        self.set_object(gen * r)

    @classmethod
    def from_random(cls: Type[T]) -> T:
        ret = cls()
        ret.set_random()
        return ret


class Fp(
    StrMixin,
    HashRandomCmpMixin,
    OneInvDivMixin,
    PowMixin,
    IntMixin,
    Base,
):
    _fields_ = [("d", ctypes.c_uint64 * ct.MCLBN_FP_UNIT_SIZE)]  # noqa


class Fr(
    StrMixin,
    HashRandomCmpMixin,
    OneInvDivMixin,
    PowMixin,
    IntMixin,
    Base,
):
    _fields_ = [("d", ctypes.c_uint64 * ct.MCLBN_FR_UNIT_SIZE)]  # noqa


class Fp2(OneInvDivMixin, Base):
    D: int = 2
    _fields_ = [("d", Fp * D)]

    @classmethod
    def byte_size(cls: Type[T]) -> int:
        return Fp.byte_size() * cls.D


class G1(
    StrMixin,
    MulFrMixin,
    MulVecMixin,
    HashAndMapMixin,
    GeneratorMixin,
    Base,
):
    _fields_ = [("x", Fp), ("y", Fp), ("z", Fp)]


class G2(
    StrMixin,
    MulFrMixin,
    MulVecMixin,
    HashAndMapMixin,
    GeneratorMixin,
    Base,
):
    _fields_ = [("x", Fp2), ("y", Fp2), ("z", Fp2)]


class GT(StrMixin, OneInvDivMixin, MulVecMixin, PowFrMixin, IntMixin, Base):
    D: int = 12
    _fields_ = [("d", Fp * D)]

    @classmethod
    def byte_size(cls: Type[T]) -> int:
        return Fp.byte_size() * cls.D

    @classmethod
    def pown(cls: Type[T], x: ctypes.Array[T], y: ctypes.Array["Fr"]) -> T:
        if len(x) != len(y):
            logging.warn(f"pown: len(x)={len(x)} != len(y)={len(y)}")
        ret = cls()
        func = ret._func(  # noqa
            "powVec",
            [ctypes.POINTER(cls)] * 2 + [ctypes.POINTER(Fr), ctypes.c_size_t],
        )
        func(ret, x, y, min(len(x), len(y)))
        return ret

    @classmethod
    def pairing(cls: Type[T], e1: "G1", e2: "G2") -> T:
        func = getattr(ct.lib, "mclBn_pairing")
        func.argtypes = [
            ctypes.POINTER(cls),
            ctypes.POINTER(G1),
            ctypes.POINTER(G2),
        ]
        ret = cls()
        func(ret, e1, e2)
        return ret

```

### pygroupsig\utils\spk.py

```
# mypy: disable-error-code="call-arg,assignment,index,operator"

import hashlib
import json
from base64 import b64decode, b64encode
from typing import Any, Type, TypeVar

from typing_extensions import Self

from pygroupsig.utils.helpers import ReprMixin
from pygroupsig.utils.mcl import G1, G2, GT, Fr

T = TypeVar("T")


class B64Mixin:
    def to_b64(self) -> str:
        dump = {}
        for v in vars(self):
            obj = getattr(self, v)
            if isinstance(obj, list):
                dump[v] = [
                    el.to_b64() if not isinstance(el, str) else f"str_{el}"
                    for el in obj
                ]
            else:
                dump[v] = obj.to_b64()
        return b64encode(json.dumps(dump).encode()).decode()

    def set_b64(self, s: str | bytes) -> None:
        if isinstance(s, str):
            s = s.encode()
        elif not isinstance(s, bytes):
            raise TypeError(f"Invalid {s} type. Expected str/bytes")
        data = json.loads(b64decode(s))
        for k, v in data.items():
            obj = getattr(self, k)
            if isinstance(data[k], list):
                obj.extend(
                    [
                        Fr.from_b64(el)
                        if not el.startswith("str_")
                        else el.split("_")[1]
                        for el in data[k]
                    ]
                )
            else:
                obj.set_b64(data[k])

    @classmethod
    def from_b64(cls: Type[T], s: str | bytes) -> T:
        ret = cls()
        ret.set_b64(s)  # type: ignore
        return ret

    def set_object(self, y: Self) -> None:
        for v in vars(y):
            s_obj = getattr(y, v)
            d_obj = getattr(self, v)
            if isinstance(s_obj, list):
                d_obj.extend(s_obj)
            else:
                d_obj.set_object(s_obj)


class DiscreteLogProof(B64Mixin, ReprMixin):
    """Data structure for convenional discrete log proofs"""

    c: Fr
    s: Fr

    def __init__(self) -> None:
        self.c = Fr()
        self.s = Fr()


class DiscreteLogProof2(B64Mixin, ReprMixin):
    """Data structure for convenional discrete log proofs"""

    c: Fr
    s: Fr
    x: list[str]

    def __init__(self) -> None:
        self.c = Fr()
        self.s = Fr()
        self.x = []


# General NIZK proofs of knowledge for CPY06
NizkProof = DiscreteLogProof


class GeneralRepresentationProof(B64Mixin, ReprMixin):
    """Data structure for general representation proofs"""

    c: Fr
    s: list[Fr]

    def __init__(self) -> None:
        self.c = Fr()
        self.s = []


class PairingHomomorphismProof(B64Mixin, ReprMixin):
    """Data structure for pairing homomorphism proofs"""

    c: Fr
    s: G2

    def __init__(self) -> None:
        self.c = Fr()
        self.s = G2()


class PairingHomomorphismProof2(B64Mixin, ReprMixin):
    """Data structure for pairing homomorphism proofs"""

    c: Fr
    s: G2
    tau: GT

    def __init__(self) -> None:
        self.c = Fr()
        self.s = G2()
        self.tau = GT()


def general_representation_sign(
    y: list[Any],
    g: list[Any],
    x: list[Fr],
    i: list[tuple[int, int]],
    prods: list[int],
    b_n: str | bytes,
    manual: bool = False,
) -> GeneralRepresentationProof:
    r = [Fr.from_random() for _ in x]

    ## Compute the challenges according to the relations defined by
    ## the i indexes
    gr = [g[j[1]] * r[j[0]] for j in i]

    ## Compute the challenge products
    prod = []
    if not manual:
        idx = 0
        for j in range(len(y)):
            prod.append(gr[idx])
            idx += 1
            if prods[j] > 1:
                ## We use prods to specify how the i indexes are 'assigned' per
                ## random 'challenge'
                for k in range(prods[j] - 1):
                    prod[j] = prod[j] + gr[idx]
                    idx += 1
    else:
        for j in range(4):
            prod.append(gr[j])
        prod.append(gr[4] + gr[5])
        prod.append(gr[6] + gr[7])

    ## Compute the hash:
    ## pi->c = Hash(msg, y[1..ny], g[1..ng], i[1,1], i[1,2] .. i[ni,1], i[ni,2], prod[1..ny])
    ## where prod[j] = g[i[j,2]]^r[i[j,1]]
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    # print("bn sign: ", b_n)

    # Push the y values
    for j in y:
        h.update(j.to_bytes())

    # Push the base values
    for j in g:
        h.update(j.to_bytes())

    # Push the indices
    for j in i:
        bi = bytearray(
            [
                j[0] & 0xFF,
                (j[0] & 0xFF00) >> 8,
                j[1] & 0xFF,
                (j[1] & 0xFF00) >> 8,
            ]
        )
        h.update(bi)

    # Push the products
    for j in prod:
        h.update(j.to_bytes())

    ## Convert the hash to an integer
    proof = GeneralRepresentationProof()
    proof.c.set_hash(h.digest())

    ## Compute challenge responses
    for idx, j in enumerate(x):
        # si = ri - cxi
        proof.s.append(r[idx] - (proof.c * j))
    return proof


def general_representation_verify(
    y: list[Any],
    g: list[Any],
    i: list[tuple[int, int]],
    prods: list[int],
    proof: GeneralRepresentationProof,
    b_n: str | bytes,
    manual: bool = False,
) -> bool:
    ## Compute the challenge products -- manually until fixing issue23
    prod = []
    if not manual:
        idx = 0
        for j in range(len(y)):
            prod.append(y[j] * proof.c)
            if prods[j] >= 1:
                ## We use prods to specify how the i indexes are 'assigned' per
                ## random 'challenge'
                for k in range(prods[j]):
                    gs = g[i[idx][1]] * proof.s[i[idx][0]]
                    prod[j] = prod[j] + gs
                    idx += 1
    else:
        for idx, j in enumerate(y):
            p = j * proof.c
            if idx == 5:
                idy = idx + 1
            else:
                idy = idx
            gs = g[i[idy][1]] * proof.s[i[idy][0]]
            prod.append(p + gs)
            if idx > 3:
                idy += 1
                gs = g[i[idy][1]] * proof.s[i[idy][0]]
                prod[-1] = prod[-1] + gs
    ## if pi is correct, then pi->c must equal:
    ## Hash(msg, y[1..ny], g[1..ng], i[1,1], i[1,2] .. i[ni,1], i[ni,2], prod[1..ny])
    ## where prod[j] = y[j]^c*g[i[j,2]]^s[i[j,1]]
    # Push the message
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    # print("bn ver: ", b_n)

    # Push the y values
    for j in y:
        h.update(j.to_bytes())

    # Push the base values
    for j in g:
        h.update(j.to_bytes())

    # Push the indices
    for j in i:
        bi = bytearray(
            [
                j[0] & 0xFF,
                (j[0] & 0xFF00) >> 8,
                j[1] & 0xFF,
                (j[1] & 0xFF00) >> 8,
            ]
        )
        h.update(bi)

    # Push the products
    for j in prod:
        h.update(j.to_bytes())

    # print(h.hexdigest())
    ## Convert the hash to an integer
    c = Fr.from_hash(h.digest())
    return c == proof.c


def discrete_log_sign(
    G: G1, g: G1, x: Fr, b_n: str | bytes
) -> DiscreteLogProof:
    ## Pick random r and compute g*r mod q
    r = Fr.from_random()
    gr = g * r

    ## Make hc = Hash(msg||G||g||g*r)
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    h.update(G.to_bytes())
    h.update(g.to_bytes())
    h.update(gr.to_bytes())

    ## Convert the hash to an integer
    proof = DiscreteLogProof()
    proof.c.set_hash(h.digest())

    # s = r - cx
    proof.s.set_object(r - (proof.c * x))
    return proof


def discrete_log_verify(
    G: G1, g: G1, proof: DiscreteLogProof, b_n: str | bytes
) -> bool:
    ## If pi (proof) is correct, then pi.c must equal Hash(msg||G||g||g*pi.s+g*pi.c)
    ## Compute g*pi.s + g*pi.c
    gsGc = (g * proof.s) + (G * proof.c)

    ## Compute the hash
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    h.update(G.to_bytes())
    h.update(g.to_bytes())
    h.update(gsGc.to_bytes())

    ## Compare the result with c
    c = Fr.from_hash(h.digest())
    return c == proof.c


def pairing_homomorphism_sign(
    g: G1, G: GT, xx: G2, b_n: str | bytes
) -> PairingHomomorphismProof:
    ## Pick random R from G2
    rr = G2.from_random()
    ## Compute the map
    R = GT.pairing(g, rr)

    ## Make hc = Hash(msg||g||G||R)
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    h.update(g.to_bytes())
    h.update(G.to_bytes())
    h.update(R.to_bytes())

    ## Convert the hash to an integer
    proof = PairingHomomorphismProof()
    proof.c.set_hash(h.digest())

    # s = rr+xx*c
    proof.s.set_object(rr + (xx * proof.c))
    return proof


def pairing_homomorphism_verify(
    g: G1, G: GT, proof: PairingHomomorphismProof, b_n: str | bytes
) -> bool:
    ## If pi is correct, then pi.c equals Hash(msg||g||G||e(g,pi.s)/G**pi.c)
    ## Compute e(g,pi.s)/G**pi.c
    R = GT.pairing(g, proof.s) / (G**proof.c)

    # Compute the hash
    h = hashlib.sha256()
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)
    h.update(g.to_bytes())
    h.update(G.to_bytes())
    h.update(R.to_bytes())

    ## Compare the result with c
    c = Fr.from_hash(h.digest())
    return c == proof.c


def pairing_homomorphism_sign2(
    xx: G2, g1: G1, g2: G1, e1: GT, e2: GT, tau: GT, b_n: str | bytes
) -> PairingHomomorphismProof2:
    # RR1 = e(g1,rr), RR2 = e(g2,rr)
    rr = G2.from_random()
    RR1 = GT.pairing(g1, rr)
    RR2 = GT.pairing(g2, rr)

    # c = Hash(g1,g2,e1,e2,RR1,RR2,msg)
    h = hashlib.sha256()
    h.update(g1.to_bytes())
    h.update(g2.to_bytes())
    h.update(e1.to_bytes())
    h.update(e2.to_bytes())
    h.update(RR1.to_bytes())
    h.update(RR2.to_bytes())
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)

    proof = PairingHomomorphismProof2()
    proof.c.set_hash(h.digest())

    # s = rr + xx*c
    proof.s.set_object(rr + (xx * proof.c))
    proof.tau.set_object(tau)
    return proof


def pairing_homomorphism_verify2(
    proof: PairingHomomorphismProof2, g1: G1, g2: G1, e1: GT, b_n: str | bytes
) -> bool:
    # RR1 = e(g1,pi.s)/e1**pi.c
    RR1 = GT.pairing(g1, proof.s) / (e1**proof.c)
    # RR2 = e(g2,pi.s)/e2**pi.c
    RR2 = GT.pairing(g2, proof.s) / (proof.tau**proof.c)
    h = hashlib.sha256()
    h.update(g1.to_bytes())
    h.update(g2.to_bytes())
    h.update(e1.to_bytes())
    h.update(proof.tau.to_bytes())
    h.update(RR1.to_bytes())
    h.update(RR2.to_bytes())
    if isinstance(b_n, str):
        b_n = b_n.encode()
    h.update(b_n)

    c = Fr.from_hash(h.digest())
    return c == proof.c

```

### pygroupsig\__init__.py

```
"""pygroupsig, python implementation of libgroupsig"""

__all__ = [
    "crl",
    "gml",
    "key",
    "group",
    "signature",
    "GroupBBS04",
    "GroupKeyBBS04",
    "ManagerKeyBBS04",
    "MemberKeyBBS04",
    "SignatureBBS04",
    "GroupPS16",
    "GroupKeyPS16",
    "ManagerKeyPS16",
    "MemberKeyPS16",
    "SignaturePS16",
    "GroupCPY06",
    "GroupKeyCPY06",
    "ManagerKeyCPY06",
    "MemberKeyCPY06",
    "SignatureCPY06",
    "GroupKLAP20",
    "GroupKeyKLAP20",
    "ManagerKeyKLAP20",
    "MemberKeyKLAP20",
    "SignatureKLAP20",
    "GroupGL19",
    "GroupKeyGL19",
    "ManagerKeyGL19",
    "MemberKeyGL19",
    "SignatureGL19",
    "GroupDL21",
    "GroupKeyDL21",
    "ManagerKeyDL21",
    "MemberKeyDL21",
    "SignatureDL21",
    "GroupDL21SEQ",
    "GroupKeyDL21SEQ",
    "ManagerKeyDL21SEQ",
    "MemberKeyDL21SEQ",
    "SignatureDL21SEQ",
]

from .definitions import group, key, signature
from .schemes.bbs04 import Group as GroupBBS04
from .schemes.bbs04 import GroupKey as GroupKeyBBS04
from .schemes.bbs04 import ManagerKey as ManagerKeyBBS04
from .schemes.bbs04 import MemberKey as MemberKeyBBS04
from .schemes.bbs04 import Signature as SignatureBBS04
from .schemes.cpy06 import Group as GroupCPY06
from .schemes.cpy06 import GroupKey as GroupKeyCPY06
from .schemes.cpy06 import ManagerKey as ManagerKeyCPY06
from .schemes.cpy06 import MemberKey as MemberKeyCPY06
from .schemes.cpy06 import Signature as SignatureCPY06
from .schemes.dl21 import Group as GroupDL21
from .schemes.dl21 import GroupKey as GroupKeyDL21
from .schemes.dl21 import ManagerKey as ManagerKeyDL21
from .schemes.dl21 import MemberKey as MemberKeyDL21
from .schemes.dl21 import Signature as SignatureDL21
from .schemes.dl21seq import Group as GroupDL21SEQ
from .schemes.dl21seq import GroupKey as GroupKeyDL21SEQ
from .schemes.dl21seq import ManagerKey as ManagerKeyDL21SEQ
from .schemes.dl21seq import MemberKey as MemberKeyDL21SEQ
from .schemes.dl21seq import Signature as SignatureDL21SEQ
from .schemes.gl19 import Group as GroupGL19
from .schemes.gl19 import GroupKey as GroupKeyGL19
from .schemes.gl19 import ManagerKey as ManagerKeyGL19
from .schemes.gl19 import MemberKey as MemberKeyGL19
from .schemes.gl19 import Signature as SignatureGL19
from .schemes.klap20 import Group as GroupKLAP20
from .schemes.klap20 import GroupKey as GroupKeyKLAP20
from .schemes.klap20 import ManagerKey as ManagerKeyKLAP20
from .schemes.klap20 import MemberKey as MemberKeyKLAP20
from .schemes.klap20 import Signature as SignatureKLAP20
from .schemes.ps16 import Group as GroupPS16
from .schemes.ps16 import GroupKey as GroupKeyPS16
from .schemes.ps16 import ManagerKey as ManagerKeyPS16
from .schemes.ps16 import MemberKey as MemberKeyPS16
from .schemes.ps16 import Signature as SignaturePS16
from .utils.constants import load_library
from .utils.helpers import CRL as crl
from .utils.helpers import GML as gml

load_library()

```

