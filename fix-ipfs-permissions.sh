#!/bin/bash
# Script to fix IPFS permissions issues

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  print_error "Docker is not running. Please start Docker first."
  exit 1
fi

# Check if the IPFS container is running
if ! docker ps | grep -q ipfs-node; then
  print_error "IPFS container is not running. Please start it first with 'docker-compose up -d ipfs'."
  exit 1
fi

# Create IPFS data directory if it doesn't exist
if [ ! -d "ipfs-data" ]; then
  print_message "Creating IPFS data directory..."
  mkdir -p ipfs-data
fi

# Fix permissions on the IPFS data directory
print_message "Fixing permissions on IPFS data directory..."
chmod -R 777 ipfs-data

# Create local storage directory if it doesn't exist
if [ ! -d "local_storage" ]; then
  print_message "Creating local storage directory..."
  mkdir -p local_storage
fi

# Fix permissions on the local storage directory
print_message "Fixing permissions on local storage directory..."
chmod -R 777 local_storage

# Restart the IPFS container
print_message "Restarting IPFS container..."
docker-compose restart ipfs

# Wait for IPFS to start
print_message "Waiting for IPFS to start..."
sleep 10

# Check if IPFS is running
print_message "Checking if IPFS is running..."
if docker exec ipfs-node ipfs id > /dev/null 2>&1; then
  print_message "IPFS is running correctly!"
else
  print_error "IPFS is not running correctly. Please check the logs with 'docker-compose logs ipfs'."
  exit 1
fi

print_message "IPFS permissions fixed successfully!"
print_message "You can now restart the API and web containers with 'docker-compose restart api web'."
