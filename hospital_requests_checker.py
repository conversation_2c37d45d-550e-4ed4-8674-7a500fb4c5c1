#!/usr/bin/env python3
"""
Hospital Requests Checker - Standalone script to check pending purchase requests
This script can be used until the server is restarted to pick up the new endpoint
"""

import json
import os
import time
from datetime import datetime

def check_pending_hospital_requests():
    """Check for pending hospital requests in local storage"""
    
    print("=== Hospital Pending Requests Checker ===")
    print(f"Checking at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    purchases_dir = "local_storage/purchases"
    
    if not os.path.exists(purchases_dir):
        print("❌ No purchases directory found")
        return []
    
    files = os.listdir(purchases_dir)
    pending_requests = []
    current_time = int(time.time())
    
    print(f"\nScanning {len(files)} files in {purchases_dir}...")
    
    for filename in files:
        if not filename.endswith('.json'):
            continue
            
        filepath = os.path.join(purchases_dir, filename)
        
        try:
            with open(filepath, 'r') as f:
                purchase_data = json.load(f)
            
            # Check if request is pending hospital confirmation
            status = purchase_data.get("status", "")
            workflow_stage = purchase_data.get("workflow_stage", "")
            
            if status in ["pending_hospital_confirmation", "pending"] or workflow_stage == "request_submitted":
                # Check if not expired
                expires_at = purchase_data.get("expires_at", 0)
                
                if expires_at > current_time:
                    # Calculate time remaining
                    time_remaining = expires_at - current_time
                    hours_remaining = time_remaining // 3600
                    minutes_remaining = (time_remaining % 3600) // 60
                    
                    request_summary = {
                        "request_id": purchase_data.get("request_id"),
                        "buyer": purchase_data.get("buyer"),
                        "buyer_address": purchase_data.get("buyer_address"),
                        "amount": purchase_data.get("amount"),
                        "escrow_amount": purchase_data.get("escrow_amount"),
                        "template": purchase_data.get("template", {}),
                        "template_hash": purchase_data.get("template_hash"),
                        "created_at": purchase_data.get("created_at"),
                        "expires_at": purchase_data.get("expires_at"),
                        "status": purchase_data.get("status"),
                        "workflow_stage": purchase_data.get("workflow_stage"),
                        "blockchain_confirmed": purchase_data.get("blockchain_confirmed", False),
                        "tx_hash": purchase_data.get("tx_hash"),
                        "time_remaining": time_remaining,
                        "time_remaining_formatted": f"{hours_remaining}h {minutes_remaining}m"
                    }
                    
                    pending_requests.append(request_summary)
                else:
                    print(f"  ⏰ Request {purchase_data.get('request_id')} has expired")
                    
        except Exception as e:
            print(f"  ❌ Error processing {filename}: {str(e)}")
            continue
    
    # Sort by creation time (newest first)
    pending_requests.sort(key=lambda x: x.get("created_at", 0), reverse=True)
    
    print(f"\n✅ Found {len(pending_requests)} pending requests for hospital confirmation:")
    
    if pending_requests:
        print("\n" + "="*80)
        for i, req in enumerate(pending_requests, 1):
            print(f"\n{i}. REQUEST ID: {req['request_id']}")
            print(f"   Buyer: {req['buyer']}")
            print(f"   Amount: {req['amount']} ETH (Escrow: {req['escrow_amount']} ETH)")
            print(f"   Status: {req['status']}")
            print(f"   Workflow: {req['workflow_stage']}")
            print(f"   Blockchain Confirmed: {'✅' if req['blockchain_confirmed'] else '❌'}")
            print(f"   TX Hash: {req['tx_hash']}")
            print(f"   Time Remaining: {req['time_remaining_formatted']}")
            
            # Template details
            template = req.get('template', {})
            if template:
                print(f"   Template Details:")
                print(f"     - Data Types: {template.get('data_types', [])}")
                print(f"     - Purpose: {template.get('purpose', 'N/A')}")
                print(f"     - Max Records: {template.get('max_records', 'N/A')}")
                print(f"     - Criteria: {template.get('criteria', {})}")
            
            print(f"   Created: {datetime.fromtimestamp(req['created_at']).strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   Expires: {datetime.fromtimestamp(req['expires_at']).strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 80)
    else:
        print("\n📭 No pending requests found")
        print("\nPossible reasons:")
        print("- No purchase requests have been made")
        print("- All requests have been confirmed or expired")
        print("- Requests are in a different status")
    
    return pending_requests

def generate_hospital_reply_command(request_id):
    """Generate a curl command for hospital to reply to a request"""
    
    hospital_address = os.getenv('HOSPITAL_ADDRESS', '******************************************')
    
    command = f'''curl -X POST "http://localhost:8000/api/purchase/reply" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "request_id": "{request_id}",
    "wallet_address": "{hospital_address}",
    "records_count": 15,
    "patients_count": 3,
    "price_per_record": 0.001
  }}'
'''
    
    return command

def main():
    """Main function"""
    
    pending_requests = check_pending_hospital_requests()
    
    if pending_requests:
        print(f"\n🏥 HOSPITAL ACTION REQUIRED:")
        print(f"You have {len(pending_requests)} pending purchase request(s) to confirm!")
        
        print(f"\n📋 To confirm a request, use the hospital interface or run:")
        for req in pending_requests[:3]:  # Show commands for first 3 requests
            print(f"\n# For Request {req['request_id']}:")
            print(generate_hospital_reply_command(req['request_id']))
        
        if len(pending_requests) > 3:
            print(f"\n... and {len(pending_requests) - 3} more requests")
    
    print(f"\n💡 TIP: To enable the hospital requests endpoint in the UI:")
    print("1. Restart the FastAPI server to pick up the new /api/hospital/requests endpoint")
    print("2. Or use this script to check for pending requests")
    print("3. Use the hospital interface to confirm requests")
    
    print(f"\n🔄 Run this script again anytime to check for new requests:")
    print("python hospital_requests_checker.py")

if __name__ == "__main__":
    main()
