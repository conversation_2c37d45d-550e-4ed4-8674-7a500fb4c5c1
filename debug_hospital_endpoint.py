#!/usr/bin/env python3
"""
Debug script to test the hospital endpoint and diagnose issues
"""

import requests
import json
import os

def test_hospital_endpoint():
    """Test the hospital endpoint and diagnose issues"""
    
    base_url = "http://localhost:8000"
    
    print("=== Debugging Hospital Endpoint ===")
    
    # Test 1: Check if server is running
    print("\n--- Test 1: Server Health Check ---")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print("❌ Server health check failed")
            return
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        return
    
    # Test 2: Check available endpoints
    print("\n--- Test 2: Available Endpoints ---")
    try:
        response = requests.get(f"{base_url}/openapi.json")
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get("paths", {})
            hospital_endpoints = [path for path in paths.keys() if "hospital" in path.lower()]
            print(f"Hospital endpoints found: {hospital_endpoints}")
            
            if "/api/hospital/requests" in paths:
                print("✅ Hospital requests endpoint is registered")
            else:
                print("❌ Hospital requests endpoint NOT found in OpenAPI")
                print("Available endpoints:")
                for path in sorted(paths.keys()):
                    print(f"  {path}")
        else:
            print("❌ Could not get OpenAPI spec")
    except Exception as e:
        print(f"❌ Error checking endpoints: {str(e)}")
    
    # Test 3: Try different URL variations
    print("\n--- Test 3: Testing URL Variations ---")
    
    hospital_address = "******************************************"
    
    urls_to_test = [
        f"{base_url}/api/hospital/requests?wallet_address={hospital_address}",
        f"{base_url}/hospital/requests?wallet_address={hospital_address}",
        f"{base_url}/api/hospital/requests",
        f"{base_url}/hospital/requests"
    ]
    
    for url in urls_to_test:
        try:
            print(f"\nTesting: {url}")
            if "?" in url:
                response = requests.get(url)
            else:
                response = requests.get(url, params={"wallet_address": hospital_address})
            
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Success: {result}")
                return result
            else:
                print(f"Response: {response.text}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    # Test 4: Check local storage directly
    print("\n--- Test 4: Direct Local Storage Check ---")
    
    try:
        purchases_dir = "local_storage/purchases"
        if os.path.exists(purchases_dir):
            files = os.listdir(purchases_dir)
            print(f"Found {len(files)} purchase files")
            
            pending_count = 0
            for filename in files:
                if filename.endswith('.json'):
                    filepath = os.path.join(purchases_dir, filename)
                    try:
                        with open(filepath, 'r') as f:
                            data = json.load(f)
                        
                        status = data.get('status', '')
                        workflow_stage = data.get('workflow_stage', '')
                        
                        if status in ["pending_hospital_confirmation", "pending"] or workflow_stage == "request_submitted":
                            pending_count += 1
                            print(f"  Pending request: {filename}")
                            print(f"    Status: {status}")
                            print(f"    Workflow: {workflow_stage}")
                            print(f"    Buyer: {data.get('buyer', 'N/A')}")
                            print(f"    Amount: {data.get('amount', 'N/A')}")
                    except Exception as e:
                        print(f"  Error reading {filename}: {str(e)}")
            
            print(f"\nTotal pending requests found: {pending_count}")
            
        else:
            print("❌ Local storage directory not found")
            
    except Exception as e:
        print(f"❌ Error checking local storage: {str(e)}")
    
    # Test 5: Try POST method (in case it's registered wrong)
    print("\n--- Test 5: Testing POST Method ---")
    try:
        response = requests.post(f"{base_url}/api/hospital/requests", 
                               json={"wallet_address": hospital_address})
        print(f"POST Status: {response.status_code}")
        print(f"POST Response: {response.text}")
    except Exception as e:
        print(f"❌ POST Error: {str(e)}")

def test_manual_implementation():
    """Manually implement the hospital request logic to test"""
    
    print("\n=== Manual Implementation Test ===")
    
    try:
        purchases_dir = "local_storage/purchases"
        if not os.path.exists(purchases_dir):
            print("❌ Purchases directory not found")
            return
        
        files = os.listdir(purchases_dir)
        pending_requests = []
        
        for filename in files:
            if not filename.endswith('.json'):
                continue
                
            filepath = os.path.join(purchases_dir, filename)
            try:
                with open(filepath, 'r') as f:
                    purchase_data = json.load(f)
                
                status = purchase_data.get("status", "")
                workflow_stage = purchase_data.get("workflow_stage", "")
                
                if status in ["pending_hospital_confirmation", "pending"] or workflow_stage == "request_submitted":
                    # Check if not expired
                    expires_at = purchase_data.get("expires_at", 0)
                    current_time = 1752615937  # Current timestamp
                    
                    if expires_at > current_time:
                        request_summary = {
                            "request_id": purchase_data.get("request_id"),
                            "buyer": purchase_data.get("buyer"),
                            "amount": purchase_data.get("amount"),
                            "status": purchase_data.get("status"),
                            "created_at": purchase_data.get("created_at"),
                            "expires_at": purchase_data.get("expires_at"),
                            "time_remaining": expires_at - current_time
                        }
                        pending_requests.append(request_summary)
                        
            except Exception as e:
                print(f"Error processing {filename}: {str(e)}")
        
        print(f"Manual implementation found {len(pending_requests)} pending requests:")
        for req in pending_requests:
            print(f"  Request {req['request_id']}: {req['buyer']} - {req['amount']} ETH")
            print(f"    Status: {req['status']}")
            print(f"    Time remaining: {req['time_remaining']} seconds")
        
        return pending_requests
        
    except Exception as e:
        print(f"❌ Manual implementation error: {str(e)}")
        return []

if __name__ == "__main__":
    print("Starting Hospital Endpoint Debug...")
    
    # Test the endpoint
    test_hospital_endpoint()
    
    # Test manual implementation
    manual_results = test_manual_implementation()
    
    if manual_results:
        print(f"\n✅ Manual implementation works - found {len(manual_results)} requests")
        print("The issue is likely with the endpoint registration or server restart needed")
    else:
        print(f"\n❌ No pending requests found even with manual implementation")
        print("The issue might be with the request creation or status setting")
