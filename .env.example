# ======================================================
# BLOCKCHAIN CONFIGURATION
# ======================================================

# Base Sepolia testnet connection
SEPOLIA_RPC_URL=https://base-sepolia-rpc.publicnode.com
CONTRACT_ADDRESS=******************************************

# ======================================================
# ACCOUNT ADDRESSES
# ======================================================

# Default account (used for deployment)
WALLET_ADDRESS=******************************************

# Role-specific addresses
PATIENT_ADDRESS=******************************************
DOCTOR_ADDRESS=******************************************
HOSPITAL_ADDRESS=******************************************
BUYER_ADDRESS=******************************************
GROUP_MANAGER_ADDRESS=******************************************
REVOCATION_MANAGER_ADDRESS=******************************************

# ======================================================
# PRIVATE KEYS
# ======================================================
# WARNING: Never commit real private keys to version control
# These are test keys for development only

# Default private key (used for deployment)
PRIVATE_KEY=91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e

# Role-specific private keys
PATIENT_PRIVATE_KEY=91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e
DOCTOR_PRIVATE_KEY=ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
HOSPITAL_PRIVATE_KEY=1e291b59ddd32689ee42459971d5f0ad1b794972be116e5fb9f1929616afeb47
BUYER_PRIVATE_KEY=e25e8f9128ba1bef33e1cacb2e1b50dd3f34c7f175b61098b4ab4f17c9416d06
GROUP_MANAGER_PRIVATE_KEY=59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d
REVOCATION_MANAGER_PRIVATE_KEY=4bf1c7cac1c53c7f7f7ddcc979b159d66a3d2d721fa4053330adbb100be628a0

# ======================================================
# IPFS CONFIGURATION
# ======================================================

# Local IPFS configuration
IPFS_URL=http://localhost:5001

# ======================================================
# API KEYS
# ======================================================

# Basescan API key for BASE Sepolia testnet
BASESCAN_API_KEY=I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y

# Etherscan API key for wallet balance retrieval
ETHERSCAN_API_KEY=**********************************

# Coinbase Cloud credentials (if needed)
# COINBASE_PROJECT_ID=your_project_id
# COINBASE_API_KEY_ID=your_api_key_id
# COINBASE_API_KEY_SECRET=your_api_key_secret
# COINBASE_CLIENT_API_KEY=your_client_api_key