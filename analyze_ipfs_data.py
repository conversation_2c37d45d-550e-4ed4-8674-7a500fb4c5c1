#!/usr/bin/env python3
"""
Script to analyze IPFS data retrieved with CID: bafkreibk6gizduaqyft3vzetbqed3p34n22sk3b33nzq7is4rzsotxcufq
"""

import base64
import json
import binascii
import sys
import os

def analyze_data(filename):
    """Analyze the binary data from IPFS"""
    
    print(f"=== Analyzing IPFS data from {filename} ===")
    
    # Read the binary data
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"Data size: {len(data)} bytes")
    print(f"First 50 bytes (hex): {data[:50].hex()}")
    print(f"Last 50 bytes (hex): {data[-50:].hex()}")
    
    # Try to decode as different formats
    print("\n=== Attempting different decodings ===")
    
    # 1. Try as UTF-8 text
    try:
        text = data.decode('utf-8')
        print("✅ Successfully decoded as UTF-8:")
        print(f"Text: {text[:200]}...")
        return
    except UnicodeDecodeError:
        print("❌ Not valid UTF-8 text")
    
    # 2. Try as base64
    try:
        # Check if it looks like base64
        if all(c in 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=' for c in data.decode('ascii', errors='ignore')):
            decoded = base64.b64decode(data)
            print("✅ Successfully decoded as base64:")
            print(f"Decoded size: {len(decoded)} bytes")
            print(f"Decoded hex: {decoded[:50].hex()}")
            
            # Try to decode the base64 result as UTF-8
            try:
                text = decoded.decode('utf-8')
                print(f"Base64 decoded text: {text[:200]}...")
                return
            except UnicodeDecodeError:
                print("Base64 decoded data is not UTF-8 text")
        else:
            print("❌ Not base64 format")
    except Exception as e:
        print(f"❌ Base64 decode failed: {e}")
    
    # 3. Try as JSON
    try:
        text = data.decode('utf-8', errors='ignore')
        json_data = json.loads(text)
        print("✅ Successfully parsed as JSON:")
        print(json.dumps(json_data, indent=2)[:500])
        return
    except Exception as e:
        print(f"❌ Not valid JSON: {e}")
    
    # 4. Check if it's encrypted data (AES-GCM format)
    print("\n=== Checking for encryption patterns ===")
    
    # AES-GCM typically has:
    # - 12 bytes IV/nonce
    # - Encrypted data
    # - 16 bytes authentication tag
    
    if len(data) >= 28:  # Minimum for IV + tag
        iv = data[:12]
        tag = data[-16:]
        ciphertext = data[12:-16]
        
        print(f"Possible AES-GCM structure:")
        print(f"  IV (12 bytes): {iv.hex()}")
        print(f"  Ciphertext ({len(ciphertext)} bytes): {ciphertext[:20].hex()}...")
        print(f"  Tag (16 bytes): {tag.hex()}")
    
    # 5. Look for common file signatures
    print("\n=== Checking file signatures ===")
    signatures = {
        b'\x89PNG': 'PNG image',
        b'GIF8': 'GIF image',
        b'\xff\xd8\xff': 'JPEG image',
        b'%PDF': 'PDF document',
        b'PK\x03\x04': 'ZIP archive',
        b'\x1f\x8b': 'GZIP compressed',
        b'{\x22': 'JSON (starts with {")',
        b'[': 'JSON array',
    }
    
    for sig, desc in signatures.items():
        if data.startswith(sig):
            print(f"✅ Detected: {desc}")
            return
    
    print("❌ No known file signature detected")
    
    # 6. Show entropy analysis
    print("\n=== Entropy analysis ===")
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1
    
    # Calculate Shannon entropy
    import math
    entropy = 0
    for count in byte_counts:
        if count > 0:
            p = count / len(data)
            entropy -= p * math.log2(p)
    
    print(f"Shannon entropy: {entropy:.2f} bits/byte")
    if entropy > 7.5:
        print("High entropy - likely encrypted or compressed data")
    elif entropy < 4:
        print("Low entropy - likely plain text or structured data")
    else:
        print("Medium entropy - could be binary data or encoded text")

def main():
    filename = "ipfs_data.bin"
    
    if not os.path.exists(filename):
        print(f"Error: {filename} not found")
        print("Please run: curl -X POST \"http://127.0.0.1:5001/api/v0/cat?arg=bafkreibk6gizduaqyft3vzetbqed3p34n22sk3b33nzq7is4rzsotxcufq\" --output ipfs_data.bin")
        sys.exit(1)
    
    analyze_data(filename)

if __name__ == "__main__":
    main()
