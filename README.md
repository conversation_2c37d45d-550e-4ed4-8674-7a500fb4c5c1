# Healthcare Data Sharing System

This project implements a secure healthcare data sharing system using blockchain technology, group signatures, and IPFS for data storage.

## System Overview

The system consists of three main components:

- **IPFS Node**: Runs in Docker for decentralized storage
- **Backend API**: FastAPI server for handling business logic
- **Frontend UI**: Streamlit web interface for user interaction

## Prerequisites

- Python 3.10
- Docker
- Git

## Setup and Installation Guide

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/Aaron1924/healthcare-data-sharing.git
cd healthcare-data-sharing
```

### 2. Set Up IPFS in Docker


#### Option 2: Manual IPFS Setup

If you prefer to set up IPFS manually:

1. Create necessary directories:

```bash
mkdir -p ipfs-data
mkdir -p local_storage
chmod -R 777 ipfs-data
chmod -R 777 local_storage
```

2. Initialize the IPFS repository:

```bash
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 init
```

3. Configure IPFS for external access:

```bash
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json API.HTTPHeaders.Access-Control-Allow-Origin '["*"]'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json API.HTTPHeaders.Access-Control-Allow-Methods '["PUT", "POST", "GET"]'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json Addresses.API '"/ip4/0.0.0.0/tcp/5001"'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json Addresses.Gateway '"/ip4/0.0.0.0/tcp/8080"'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config Datastore.StorageMax "10GB"
```

4. Start the IPFS container:

```bash
docker run -d \
  --name ipfs-node \
  -p 4001:4001 \
  -p 5001:5001 \
  -p 8080:8080 \
  -v "$(pwd)/ipfs-data:/data/ipfs" \
  -v "$(pwd)/local_storage:/export" \
  -e IPFS_PROFILE=server \
  -e IPFS_PATH=/data/ipfs \
  --user 1000:1000 \
  ipfs/kubo:v0.22.0 \
  daemon --migrate=true --enable-gc
```

5. Verify IPFS is running:

```bash
# Check if the container is running
docker ps | grep ipfs

# Test the API
curl -X POST "http://localhost:5001/api/v0/id"
```

### 3. Set Up Python Environment

```bash
# Create and activate a virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 4. Configure Environment Variables

Create a `.env` file in the project root with the following variables:

```bash
# Blockchain Configuration
SEPOLIA_RPC_URL=https://base-sepolia-rpc.publicnode.com
CONTRACT_ADDRESS=0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA

# Account Addresses and Private Keys
PATIENT_ADDRESS=0x...
DOCTOR_ADDRESS=0x...
HOSPITAL_ADDRESS=0x...
GROUP_MANAGER_ADDRESS=0x...
REVOCATION_MANAGER_ADDRESS=0x...
BUYER_ADDRESS=0x...

PATIENT_PRIVATE_KEY=0x...
DOCTOR_PRIVATE_KEY=0x...
HOSPITAL_PRIVATE_KEY=0x...
GROUP_MANAGER_PRIVATE_KEY=0x...
REVOCATION_MANAGER_PRIVATE_KEY=0x...
BUYER_PRIVATE_KEY=0x...

# IPFS Configuration
IPFS_URL=http://localhost:5001

# API Keys
BASESCAN_API_KEY=I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y
ETHERSCAN_API_KEY=**********************************
```

Replace the placeholder addresses and private keys with your actual values.

### 5. Generate Group Signature Keys

```bash
# Run the key generation script
python regenerate_keys.py
```

## Running the Application

### 1. Start the Backend API

```bash
# Start the FastAPI backend
python -m uvicorn backend.api:app --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`.

### 2. Start the Frontend UI

In a new terminal:

```bash
# Activate the virtual environment if not already activated
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the Streamlit frontend
cd app
streamlit run main.py
```

The web interface will be available at `http://localhost:8501`.

## Using the Application

The system has six roles:

- **Patient**: View and share medical records
- **Doctor**: Create and sign medical records
- **Hospital**: Verify and confirm data requests
- **Group Manager**: Manage doctor group memberships
- **Revocation Manager**: Handle revocation of group memberships
- **Buyer**: Request and purchase healthcare data

Select your role from the sidebar in the web interface to access the corresponding functionality.

## Troubleshooting

### IPFS Issues

#### Common IPFS Errors and Solutions

1. **"Error: no IPFS repo found in /data/ipfs. please run: 'ipfs init'"**

   Run the setup script to initialize the repository:

   ```bash
   ./setup-ipfs.sh
   ```

2. **"Error loading plugins: open /var/ipfs/config: permission denied"**

   Fix permissions and restart the container:

   ```bash
   # Stop and remove the container
   docker stop ipfs-node
   docker rm ipfs-node

   # Fix permissions
   chmod -R 777 ipfs-data
   chmod -R 777 local_storage

   # Restart with the correct configuration
   ./setup-ipfs.sh
   ```

3. **IPFS CLI commands fail but HTTP API works**

   This is normal if you're running IPFS in Docker but the application locally. The system includes fallbacks to use the HTTP API when CLI commands fail. You'll see messages like:

   ```text
   Warning: Error adding to IPFS via CLI: Error: no IPFS repo found in /data/ipfs
   Trying to add file using IPFS HTTP API at http://localhost:5001/api/v0/add
   Successfully added to IPFS via HTTP API with CID: bafkrei...
   ```

   This means the system is working correctly, using the HTTP API as a fallback.

#### Checking IPFS Content

To check if a file was successfully stored in IPFS:

```bash
# View content by CID
curl -X POST "http://localhost:5001/api/v0/cat?arg=<CID>"

# Or using the IPFS gateway in a browser
http://localhost:8080/ipfs/<CID>
```

### Blockchain Issues

1. **Insufficient funds for gas**

   Get testnet ETH from the [BASE Sepolia Faucet](https://portal.cdp.coinbase.com/products/faucet?projectId=05614f86-3dbd-45d9-be9d-69ceeb939336).

2. **Transaction fails**

   Check that you're using the correct account and have sufficient funds:

   ```bash
   # Check balance
   python -c "from web3 import Web3; w3 = Web3(Web3.HTTPProvider('https://base-sepolia-rpc.publicnode.com')); print(f'Balance: {w3.from_wei(w3.eth.get_balance(\"YOUR_ADDRESS\"), \"ether\")} ETH')"
   ```

## License

[MIT License](LICENSE)
