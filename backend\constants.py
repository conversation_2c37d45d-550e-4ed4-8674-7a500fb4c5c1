"""
Constants for the healthcare data sharing platform.

This module defines constants used throughout the application, including
default addresses for each role and role definitions.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Role definitions
ROLES = {
    "PATIENT": "patient",
    "DOCTOR": "doctor",
    "HOSPITAL": "hospital",
    "BUY<PERSON>": "buyer",
    "GROUP_MANAGER": "group_manager",
    "REVOCATION_MANAGER": "revocation_manager"
}

# Default addresses for each role - read from .env file with no hardcoded defaults
PATIENT_ADDRESS = os.getenv("PATIENT_ADDRESS")
DOCTOR_ADDRESS = os.getenv("DOCTOR_ADDRESS")
HOSPITAL_ADDRESS = os.getenv("HOSPITAL_ADDRESS")
BUYER_ADDRESS = os.getenv("BUYER_ADDRESS")
GROUP_MANAGER_ADDRESS = os.getenv("GROUP_MANAGER_ADDRESS")
REVOCATION_MANAGER_ADDRESS = os.getenv("REVOCATION_MANAGER_ADDRESS")

# Default wallet address (for backward compatibility)
WALLET_ADDRESS = os.getenv("WALLET_ADDRESS", PATIENT_ADDRESS)

# Contract address
CONTRACT_ADDRESS = os.getenv("CONTRACT_ADDRESS")

# RPC URL for BASE Sepolia
SEPOLIA_RPC_URL = os.getenv("SEPOLIA_RPC_URL")

# IPFS URL
IPFS_URL = os.getenv("IPFS_URL")

# Private keys
PRIVATE_KEY = os.getenv("PRIVATE_KEY")
PATIENT_PRIVATE_KEY = os.getenv("PATIENT_PRIVATE_KEY")
DOCTOR_PRIVATE_KEY = os.getenv("DOCTOR_PRIVATE_KEY")
HOSPITAL_PRIVATE_KEY = os.getenv("HOSPITAL_PRIVATE_KEY")
BUYER_PRIVATE_KEY = os.getenv("BUYER_PRIVATE_KEY")
GROUP_MANAGER_PRIVATE_KEY = os.getenv("GROUP_MANAGER_PRIVATE_KEY")
REVOCATION_MANAGER_PRIVATE_KEY = os.getenv("REVOCATION_MANAGER_PRIVATE_KEY")

# Check if required environment variables are set
def check_required_env_vars():
    """Check if all required environment variables are set and print warnings for missing ones"""
    required_vars = {
        "PATIENT_ADDRESS": PATIENT_ADDRESS,
        "DOCTOR_ADDRESS": DOCTOR_ADDRESS,
        "HOSPITAL_ADDRESS": HOSPITAL_ADDRESS,
        "BUYER_ADDRESS": BUYER_ADDRESS,
        "GROUP_MANAGER_ADDRESS": GROUP_MANAGER_ADDRESS,
        "REVOCATION_MANAGER_ADDRESS": REVOCATION_MANAGER_ADDRESS,
        "CONTRACT_ADDRESS": CONTRACT_ADDRESS,
        "SEPOLIA_RPC_URL": SEPOLIA_RPC_URL,
        "PRIVATE_KEY": PRIVATE_KEY
    }

    missing_vars = [var for var, value in required_vars.items() if value is None]

    if missing_vars:
        print("WARNING: The following required environment variables are not set:")
        for var in missing_vars:
            print(f"  - {var}")
        print("Please set these variables in your .env file.")
        print("Using default values where available, but this may cause issues.")

    return len(missing_vars) == 0

# Run the check when this module is imported
all_vars_set = check_required_env_vars()
