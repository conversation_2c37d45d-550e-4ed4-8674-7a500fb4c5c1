#!/usr/bin/env python3
"""
Simple test to verify Hospital UI can find requests
"""

import requests
import json
import os

def test_hospital_api():
    """Test the hospital API endpoint"""
    
    base_url = "http://localhost:8000"
    hospital_address = "******************************************"
    
    print("=== Testing Hospital API ===")
    
    try:
        # Test the new hospital requests API endpoint (same as UI does)
        response = requests.get(
            f"{base_url}/api/hospital/requests",
            params={"wallet_address": hospital_address}
        )
        
        print(f"API Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            api_requests = result.get("requests", [])
            total_count = result.get("total_count", 0)
            
            print(f"✅ Hospital API working")
            print(f"Total pending requests: {total_count}")
            
            if api_requests:
                print("\nPending Requests Found:")
                for i, req in enumerate(api_requests, 1):
                    print(f"  {i}. Request ID: {req.get('request_id')}")
                    print(f"     Buyer: {req.get('buyer')}")
                    print(f"     Amount: {req.get('amount')} ETH")
                    print(f"     Status: {req.get('status')}")
                    print(f"     Workflow: {req.get('workflow_stage')}")
                    
                    # Convert to UI format (same as main.py does)
                    ui_request = {
                        "request_id": req.get("request_id"),
                        "buyer": req.get("buyer", "Unknown"),
                        "template_hash": req.get("template_hash", ""),
                        "amount": req.get("amount", 0.001),
                        "timestamp": req.get("created_at", 0),
                        "template": req.get("template", {
                            "category": "General",
                            "demographics": {},
                            "medical_data": {},
                            "time_period": "1 year",
                            "min_records": 10
                        })
                    }
                    
                    print(f"     UI Format: {ui_request}")
                    print()
                
                return True
            else:
                print("❌ No pending requests found")
                return False
        else:
            print(f"❌ API Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API Exception: {str(e)}")
        return False

def test_local_storage_fallback():
    """Test the local storage fallback (same as UI does)"""
    
    print("\n=== Testing Local Storage Fallback ===")
    
    try:
        # Check if the local storage directory exists
        if os.path.exists("local_storage/purchases"):
            # Get all purchase files
            purchase_files = os.listdir("local_storage/purchases")
            print(f"Found {len(purchase_files)} files in local_storage/purchases")
            
            pending_requests = []
            
            for file_name in purchase_files:
                if not file_name.endswith(".json"):
                    continue
                
                file_path = f"local_storage/purchases/{file_name}"
                
                try:
                    with open(file_path, "r") as f:
                        purchase_data = json.load(f)
                    
                    # Check if this is a pending request (same logic as UI)
                    status = purchase_data.get("status", "")
                    workflow_stage = purchase_data.get("workflow_stage", "")
                    
                    if status in ["pending", "pending_hospital_confirmation"] or workflow_stage == "request_submitted":
                        print(f"Found pending request: {purchase_data.get('request_id')}")
                        
                        # Convert to request format (same as UI)
                        new_request = {
                            "request_id": purchase_data.get("request_id"),
                            "buyer": purchase_data.get("buyer", "Unknown"),
                            "template_hash": purchase_data.get("template_hash", ""),
                            "amount": purchase_data.get("amount", 0.1),
                            "timestamp": purchase_data.get("timestamp", 0),
                            "template": purchase_data.get("template", {
                                "category": "General",
                                "demographics": {},
                                "medical_data": {},
                                "time_period": "1 year",
                                "min_records": 10
                            })
                        }
                        
                        pending_requests.append(new_request)
                        
                except Exception as e:
                    print(f"Error processing file {file_name}: {str(e)}")
            
            print(f"✅ Local storage fallback found {len(pending_requests)} pending requests")
            
            for req in pending_requests:
                print(f"  - Request {req['request_id']}: {req['buyer']} - {req['amount']} ETH")
            
            return len(pending_requests) > 0
        else:
            print("❌ Local storage directory not found")
            return False
            
    except Exception as e:
        print(f"❌ Local storage error: {str(e)}")
        return False

def main():
    """Main test function"""
    
    print("Testing Hospital UI Integration...")
    print("This simulates exactly what the Hospital UI does")
    print()
    
    # Test 1: API endpoint (primary method)
    api_success = test_hospital_api()
    
    # Test 2: Local storage fallback
    fallback_success = test_local_storage_fallback()
    
    print("\n=== Test Results ===")
    
    if api_success:
        print("✅ Hospital UI will use API endpoint (preferred)")
        print("✅ Hospital should see pending requests in the UI")
    elif fallback_success:
        print("⚠️ API failed but fallback works")
        print("✅ Hospital should see pending requests via fallback")
    else:
        print("❌ Both API and fallback failed")
        print("❌ Hospital UI will not see any requests")
        print("\nTroubleshooting:")
        print("1. Make sure FastAPI server is running")
        print("2. Create a purchase request using Buyer interface")
        print("3. Check that request has correct status")
    
    print("\nNext steps:")
    print("1. Restart Streamlit app to pick up frontend changes")
    print("2. Go to Hospital role in the UI")
    print("3. Click 'Check for New Requests'")
    print("4. Should see pending requests listed")

if __name__ == "__main__":
    main()
