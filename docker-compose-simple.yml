version: '3.8'

services:
  # Streamlit Web UI
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: healthcare-web
    ports:
      - "0.0.0.0:8501:8501"  # Streamlit UI - accessible externally
    volumes:
      - ./app:/app/app
      - ./backend:/app/backend
      - ./pygroupsig:/app/pygroupsig
      - ./artifacts:/app/artifacts
      - ./local_storage:/app/local_storage
      - ./keys:/app/keys
    depends_on:
      api:
        condition: service_started
    environment:
      - API_URL=http://api:8000/api
      - PYTHONPATH=/app
      - PRIVATE_KEY=${PRIVATE_KEY:-91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e}
      - SEPOLIA_RPC_URL=${SEPOLIA_RPC_URL:-https://base-sepolia-rpc.publicnode.com}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA}
      - DOCTOR_PRIVATE_KEY=${DOCTOR_PRIVATE_KEY:-0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80}
      - PATIENT_PRIVATE_KEY=${PATIENT_PRIVATE_KEY:-0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d}
      - HOSPITAL_PRIVATE_KEY=${HOSPITAL_PRIVATE_KEY:-0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a}
      - GROUP_MANAGER_PRIVATE_KEY=${GROUP_MANAGER_PRIVATE_KEY:-0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6}
      - REVOCATION_MANAGER_PRIVATE_KEY=${REVOCATION_MANAGER_PRIVATE_KEY:-0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a}
      - BUYER_PRIVATE_KEY=${BUYER_PRIVATE_KEY:-0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba}
    networks:
      - healthcare-network
    restart: unless-stopped
    command: streamlit run app/main.py --server.maxUploadSize=50 --server.maxMessageSize=50 --server.enableCORS=false --server.enableXsrfProtection=false
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8501 || exit 0"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # FastAPI Backend
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: healthcare-api
    ports:
      - "0.0.0.0:8000:8000"  # FastAPI - accessible externally
    volumes:
      - ./backend:/app/backend
      - ./pygroupsig:/app/pygroupsig
      - ./artifacts:/app/artifacts
      - ./local_storage:/app/local_storage
      - ./keys:/app/keys
    depends_on:
      fileserver:
        condition: service_started
    environment:
      - FILE_SERVER_URL=http://fileserver:80
      - USE_LOCAL_STORAGE=true
      - SEPOLIA_RPC_URL=${SEPOLIA_RPC_URL:-https://base-sepolia-rpc.publicnode.com}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA}
      - PRIVATE_KEY=${PRIVATE_KEY:-91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e}
      - MCL_LIB_PATH=/usr/local/lib/mcl
      - PYTHONPATH=/app
      - DOCTOR_PRIVATE_KEY=${DOCTOR_PRIVATE_KEY:-0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80}
      - PATIENT_PRIVATE_KEY=${PATIENT_PRIVATE_KEY:-0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d}
      - HOSPITAL_PRIVATE_KEY=${HOSPITAL_PRIVATE_KEY:-0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a}
      - GROUP_MANAGER_PRIVATE_KEY=${GROUP_MANAGER_PRIVATE_KEY:-0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6}
      - REVOCATION_MANAGER_PRIVATE_KEY=${REVOCATION_MANAGER_PRIVATE_KEY:-0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a}
      - BUYER_PRIVATE_KEY=${BUYER_PRIVATE_KEY:-0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba}
      - BASESCAN_API_KEY=${BASESCAN_API_KEY:-I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y}
    networks:
      - healthcare-network
    restart: unless-stopped
    command: >
      sh -c "
        # Wait for file server to be ready
        echo 'Waiting for file server to be ready...'
        sleep 5

        # Create necessary directories
        mkdir -p /app/local_storage/records
        mkdir -p /app/local_storage/purchases
        mkdir -p /app/local_storage/transactions
        mkdir -p /app/local_storage/store_transactions
        mkdir -p /app/local_storage/share_transactions
        mkdir -p /app/local_storage/purchase_transactions

        # Check if MCL library is properly installed
        echo 'Checking MCL library...'
        ls -la /usr/local/lib/mcl || true
        echo 'Checking MCL library in alternate location...'
        ls -la /app/mcl/lib || true
        echo 'Checking MCL library in build location...'
        ls -la /app/mcl/build/lib || true

        # Start the API server with optimized settings
        echo 'Starting API server with optimized settings...'
        # Use workers for better performance and disable reload for stability
        uvicorn backend.api:app --host 0.0.0.0 --port 8000 --workers 2 --limit-concurrency 50 --timeout-keep-alive 30
      "
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:8000/ || exit 0"]
      interval: 60s
      timeout: 30s
      retries: 10
      start_period: 120s

  # Simple file server (alternative to IPFS)
  fileserver:
    image: nginx:alpine
    container_name: fileserver
    ports:
      - "0.0.0.0:8080:80"  # HTTP server - accessible externally
    volumes:
      - ./local_storage:/usr/share/nginx/html
    networks:
      - healthcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

networks:
  healthcare-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "0.0.0.0"
      com.docker.network.driver.mtu: "1500"
