{"API": {"HTTPHeaders": {"Access-Control-Allow-Methods": ["PUT", "POST", "GET"], "Access-Control-Allow-Origin": ["*"]}}, "Addresses": {"API": "/ip4/0.0.0.0/tcp/5001", "Announce": [], "AppendAnnounce": [], "Gateway": "/ip4/0.0.0.0/tcp/8080", "NoAnnounce": [], "Swarm": ["/ip4/0.0.0.0/tcp/4001", "/ip6/::/tcp/4001", "/ip4/0.0.0.0/udp/4001/quic", "/ip4/0.0.0.0/udp/4001/quic-v1", "/ip4/0.0.0.0/udp/4001/quic-v1/webtransport", "/ip6/::/udp/4001/quic", "/ip6/::/udp/4001/quic-v1", "/ip6/::/udp/4001/quic-v1/webtransport"]}, "AutoNAT": {}, "Bootstrap": ["/dnsaddr/bootstrap.libp2p.io/p2p/QmbLHAnMoJPWSCR5Zhtx6BHJX9KiKNN6tpvbUcqanj75Nb", "/dnsaddr/bootstrap.libp2p.io/p2p/QmcZf59bWwK5XFi76CZX8cbJ4BhTzzA3gU1ZjYZcYW3dwt", "/ip4/**************/tcp/4001/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ", "/ip4/**************/udp/4001/quic/p2p/QmaCpDMGvV2BGHeYERUEnRQAwe3N8SzbUtfsmvsqQLuvuJ", "/dnsaddr/bootstrap.libp2p.io/p2p/QmNnooDu7bfjPFoTZYxMNLWUQJyrVwtbZg5gBMjTezGAJN", "/dnsaddr/bootstrap.libp2p.io/p2p/QmQCU2EcMqAqQPR2i9bChDtGNJchTbq5TbXJJ16u19uLTa"], "DNS": {"Resolvers": {}}, "Datastore": {"BloomFilterSize": 0, "GCPeriod": "1h", "HashOnRead": false, "Spec": {"mounts": [{"child": {"path": "blocks", "shardFunc": "/repo/flatfs/shard/v1/next-to-last/2", "sync": true, "type": "flatfs"}, "mountpoint": "/blocks", "prefix": "flatfs.datastore", "type": "measure"}, {"child": {"compression": "none", "path": "datastore", "type": "levelds"}, "mountpoint": "/", "prefix": "leveldb.datastore", "type": "measure"}], "type": "mount"}, "StorageGCWatermark": 90, "StorageMax": "10GB"}, "Discovery": {"MDNS": {"Enabled": true}}, "Experimental": {"FilestoreEnabled": false, "GraphsyncEnabled": false, "Libp2pStreamMounting": false, "OptimisticProvide": false, "OptimisticProvideJobsPoolSize": 0, "P2pHttpProxy": false, "StrategicProviding": false, "UrlstoreEnabled": false}, "Gateway": {"APICommands": [], "DeserializedResponses": null, "HTTPHeaders": {}, "NoDNSLink": false, "NoFetch": false, "PathPrefixes": [], "PublicGateways": null, "RootRedirect": ""}, "Identity": {"PeerID": "12D3KooWFEA1seahA9JpN4stujp74fpcJ4aCf9iWndktACrPUTFR", "PrivKey": "CAESQGueS5840hezS9U+rqTaKyggC3Y2gPzAbFlgUcjIIqo3UGKh9z/zNBfmF2pgw56ZGQCzQEvzqruHEOnzZjh8mmQ="}, "Internal": {}, "Ipns": {"RecordLifetime": "", "RepublishPeriod": "", "ResolveCacheSize": 128}, "Migration": {"DownloadSources": [], "Keep": ""}, "Mounts": {"FuseAllowOther": false, "IPFS": "/ipfs", "IPNS": "/ipns"}, "Peering": {"Peers": null}, "Pinning": {"RemoteServices": {}}, "Plugins": {"Plugins": null}, "Provider": {"Strategy": ""}, "Pubsub": {"DisableSigning": false, "Router": ""}, "Reprovider": {}, "Routing": {"AcceleratedDHTClient": false, "Methods": null, "Routers": null}, "Swarm": {"AddrFilters": null, "ConnMgr": {}, "DisableBandwidthMetrics": false, "DisableNatPortMap": false, "RelayClient": {}, "RelayService": {}, "ResourceMgr": {}, "Transports": {"Multiplexers": {}, "Network": {}, "Security": {}}}}