#!/usr/bin/env python3
"""
Test script to verify the purchase request functionality works correctly
"""

import requests
import json
import time

def test_purchase_request():
    """Test the purchase request endpoint with different scenarios"""
    
    base_url = "http://localhost:8000"
    
    print("=== Testing Purchase Request Functionality ===")
    
    # Test 1: Purchase request with minimal data (should use defaults)
    print("\n--- Test 1: Minimal Purchase Request ---")
    
    minimal_request = {
        "wallet_address": "******************************************",
        "template_hash": "QmTestTemplateHash123456789",
        "amount": 0.005
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/request", json=minimal_request)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'Request created')}")
            print(f"Request ID: {result.get('request_id', 'N/A')}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test 2: Purchase request with complete template
    print("\n--- Test 2: Complete Template Purchase Request ---")
    
    complete_request = {
        "wallet_address": "******************************************",
        "template_hash": "QmCompleteTemplateHash123456789",
        "template": {
            "data_types": ["diagnosis", "treatment", "medication"],
            "criteria": {
                "age": {"min": 18, "max": 65},
                "gender": ["M", "F"]
            },
            "max_records": 50,
            "purpose": "clinical_trial"
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/request", json=complete_request)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'Request created')}")
            print(f"Request ID: {result.get('request_id', 'N/A')}")
            print(f"Calculated Cost: {result.get('estimated_cost', 'N/A')} ETH")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test 3: Purchase request with partial template (should be normalized)
    print("\n--- Test 3: Partial Template Purchase Request ---")
    
    partial_request = {
        "wallet_address": "******************************************",
        "template_hash": "QmPartialTemplateHash123456789",
        "template": {
            "data_types": ["diagnosis"],
            "purpose": "research"
            # Missing criteria and max_records - should get defaults
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/request", json=partial_request)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success: {result.get('message', 'Request created')}")
            print(f"Request ID: {result.get('request_id', 'N/A')}")
            print(f"Calculated Cost: {result.get('estimated_cost', 'N/A')} ETH")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    # Test 4: Purchase request with invalid template (should be corrected)
    print("\n--- Test 4: Invalid Template Purchase Request ---")
    
    invalid_request = {
        "wallet_address": "******************************************",
        "template_hash": "QmInvalidTemplateHash123456789",
        "template": {
            "data_types": ["invalid_type", "diagnosis"],  # One invalid type
            "criteria": "not_a_dict",  # Wrong type
            "max_records": -5,  # Invalid value
            "purpose": "invalid_purpose"  # Invalid purpose
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/purchase/request", json=invalid_request)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success (normalized): {result.get('message', 'Request created')}")
            print(f"Request ID: {result.get('request_id', 'N/A')}")
            print(f"Calculated Cost: {result.get('estimated_cost', 'N/A')} ETH")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")

def test_template_validation():
    """Test the template validation directly"""
    
    print("\n=== Testing Template Validation ===")
    
    # This would require importing the validation function
    # For now, we'll just test through the API
    
    test_templates = [
        {
            "name": "Empty Template",
            "template": {}
        },
        {
            "name": "Minimal Valid Template",
            "template": {
                "data_types": ["diagnosis"],
                "criteria": {},
                "max_records": 10,
                "purpose": "research"
            }
        },
        {
            "name": "Complex Valid Template",
            "template": {
                "data_types": ["diagnosis", "treatment", "lab_results"],
                "criteria": {
                    "age": {"min": 25, "max": 60},
                    "gender": ["F"],
                    "condition": ["diabetes", "hypertension"]
                },
                "max_records": 100,
                "purpose": "clinical_trial"
            }
        }
    ]
    
    for test_case in test_templates:
        print(f"\n--- Testing: {test_case['name']} ---")
        
        request_data = {
            "wallet_address": "******************************************",
            "template_hash": f"QmTest{test_case['name'].replace(' ', '')}Hash",
            "template": test_case['template']
        }
        
        try:
            response = requests.post("http://localhost:8000/api/purchase/request", json=request_data)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Template accepted and normalized")
                print(f"Estimated cost: {result.get('estimated_cost', 'N/A')} ETH")
            else:
                print(f"❌ Template rejected: {response.text}")
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")

if __name__ == "__main__":
    print("Starting Purchase Request Tests...")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print("Press Ctrl+C to cancel, or Enter to continue...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\nTest cancelled.")
        exit(0)
    
    test_purchase_request()
    test_template_validation()
    
    print("\n=== Tests Completed ===")
    print("Check the server logs for detailed processing information.")
